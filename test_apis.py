import os
from dotenv import load_dotenv
from utils.geocoding import geocode_address
from utils.ai_summary import generate_neighborhood_summary
import pandas as pd

# Load environment variables
load_dotenv()

# Check if API keys are loaded
print("=== API Keys Check ===")
google_key = os.getenv('GOOGLE_API_KEY')
gemini_key = os.getenv('GEMINI_API_KEY')
print(f"Google API Key: {'✅ Found' if google_key else '❌ Missing'} ({len(google_key) if google_key else 0} chars)")
print(f"Gemini API Key: {'✅ Found' if gemini_key else '❌ Missing'} ({len(gemini_key) if gemini_key else 0} chars)")

# Test geocoding
print("\n=== Testing Geocoding ===")
result = geocode_address("1600 Amphitheatre Parkway, Mountain View, CA")
print(f"Result: {result}")

# Test Gemini with complete data
print("\n=== Testing Gemini ===")
test_df = pd.DataFrame([
    {
        'type': 'center',
        'yearly_energy_kwh': 0,
        'potential_category': 'Existing Solar',
        'carbon_offset_kg': 0
    },
    {
        'type': 'neighbor',
        'yearly_energy_kwh': 5000,
        'potential_category': 'Good',
        'carbon_offset_kg': 2500
    },
    {
        'type': 'neighbor',
        'yearly_energy_kwh': 7000,
        'potential_category': 'Excellent',
        'carbon_offset_kg': 3500
    }
])

try:
    summary = generate_neighborhood_summary(test_df, "Test Address")
    print("✅ AI Summary generated successfully!")
    print(f"Summary preview: {summary[:100]}...")
except Exception as e:
    print(f"❌ Error: {e}")