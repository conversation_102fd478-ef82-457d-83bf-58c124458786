import streamlit as st
import folium
from streamlit_folium import st_folium
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import io

# Import utilities
from utils.geocoding import geocode_address
from utils.grid_generator import generate_nearby_homes
from utils.solar_api import get_solar_potential, categorize_potential
from utils.ai_summary import generate_neighborhood_summary

# Page configuration
st.set_page_config(
    page_title="Solar Neighborhood Analyzer",
    page_icon="☀️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #FF6B6B;
        text-align: center;
        margin-bottom: 2rem;
    }
    .stButton>button {
        width: 100%;
        background-color: #FF6B6B;
        color: white;
    }
    .info-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'analysis_complete' not in st.session_state:
    st.session_state.analysis_complete = False
if 'solar_data' not in st.session_state:
    st.session_state.solar_data = None

# Header
st.markdown("<h1 style='text-align: center;'>☀️ Solar Neighborhood Analyzer</h1>", unsafe_allow_html=True)
st.markdown("<p style='text-align: center; font-size: 1.2rem;'>Find the best solar installation candidates in your neighborhood</p>", unsafe_allow_html=True)

# Sidebar
with st.sidebar:
    st.header("🏠 Configuration")
    
    # Address input
    address = st.text_input(
        "Enter Address with Solar",
        value="1600 Amphitheatre Parkway, Mountain View, CA",
        help="Enter the address of a home that already has solar panels"
    )
    
    # Analysis parameters
    st.subheader("⚙️ Analysis Parameters")
    
    num_homes = st.select_slider(
        "Number of nearby homes",
        options=[4, 6, 8, 10, 12],
        value=8,
        help="How many neighboring homes to analyze"
    )
    
    radius = st.slider(
        "Search radius (meters)",
        min_value=50,
        max_value=200,
        value=100,
        step=25,
        help="Distance from the reference home"
    )
    
    # Analyze button
    analyze_btn = st.button(
        "🔍 Analyze Neighborhood",
        type="primary",
        use_container_width=True
    )
    
    # Info section
    st.markdown("---")
    st.info("""
    **How it works:**
    1. Enter an address with solar
    2. We find nearby homes
    3. Analyze solar potential
    4. Rank & visualize results
    """)

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    # Map placeholder
    map_placeholder = st.empty()
    
    # Results table placeholder
    results_placeholder = st.empty()

with col2:
    # Stats cards placeholders
    stats_placeholder = st.empty()
    
    # Chart placeholder
    chart_placeholder = st.empty()

# AI Summary placeholder
summary_placeholder = st.empty()

# Process analysis
if analyze_btn:
    # Reset state
    st.session_state.analysis_complete = False
    
    # Progress tracking
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    # Step 1: Geocode address
    status_text.text("📍 Geocoding address...")
    progress_bar.progress(10)
    
    geo_result = geocode_address(address)
    
    if not geo_result['success']:
        st.error(f"❌ {geo_result['error']}")
        st.stop()
    
    center_lat = geo_result['lat']
    center_lng = geo_result['lng']
    formatted_address = geo_result['formatted_address']
    
    # Step 2: Generate nearby homes
    status_text.text("🏘️ Identifying nearby homes...")
    progress_bar.progress(20)
    
    homes = generate_nearby_homes(center_lat, center_lng, num_homes, radius)
    
    # Step 3: Analyze solar potential
    status_text.text("☀️ Analyzing solar potential...")
    solar_results = []
    
    for i, home in enumerate(homes):
        progress = 20 + (60 * (i + 1) / len(homes))
        progress_bar.progress(int(progress))
        status_text.text(f"☀️ Analyzing home {i + 1} of {len(homes)}...")
        
        # Get solar data
        solar_data = get_solar_potential(home['lat'], home['lng'])
        
        if solar_data['success']:
            # Merge home and solar data
            result = {**home, **solar_data}
            
            # Categorize potential
            if home['type'] == 'center':
                result['potential_category'] = 'Existing Solar'
                result['color'] = '#0066CC'
                result['icon'] = '🔵'
                result['priority'] = 0
            else:
                category_info = categorize_potential(solar_data['yearly_energy_kwh'])
                result.update(category_info)
            
            solar_results.append(result)
    
    # Create DataFrame
    df = pd.DataFrame(solar_results)
    
    # Sort by priority (best opportunities first)
    df_sorted = df.sort_values('priority')
    
    # Step 4: Generate AI summary
    status_text.text("🤖 Generating insights...")
    progress_bar.progress(90)
    
    ai_summary = generate_neighborhood_summary(df, formatted_address)
    
    # Complete
    progress_bar.progress(100)
    status_text.text("✅ Analysis complete!")
    
    # Store in session state
    st.session_state.analysis_complete = True
    st.session_state.solar_data = df_sorted
    st.session_state.ai_summary = ai_summary
    st.session_state.formatted_address = formatted_address
    
    # Clear progress indicators
    progress_bar.empty()
    status_text.empty()

# Display results if analysis is complete
if st.session_state.analysis_complete:
    df = st.session_state.solar_data
    
    # Create map
    with map_placeholder.container():
        st.subheader("🗺️ Neighborhood Solar Map")
        
        # Create folium map
        m = folium.Map(
            location=[df.iloc[0]['lat'], df.iloc[0]['lng']],
            zoom_start=17,
            tiles='OpenStreetMap'
        )
        
        # Add markers
        for idx, row in df.iterrows():
            # Create popup text
            popup_text = f"""
            <div style='width: 200px;'>
                <b>{row['label']}</b><br>
                {row['icon']} {row.get('potential_category', 'Unknown')}<br>
                ⚡ {row['yearly_energy_kwh']:,.0f} kWh/year<br>
                🏠 {row['roof_area_sqm']:.0f} sqm roof<br>
                🌱 {row['carbon_offset_kg']:,.0f} kg CO₂ offset
            </div>
            """
            
            # Add marker
            folium.CircleMarker(
                location=[row['lat'], row['lng']],
                radius=15 if row['type'] == 'center' else 10,
                popup=folium.Popup(popup_text, max_width=250),
                color='black',
                fill=True,
                fillColor=row['color'],
                fillOpacity=0.8,
                weight=2
            ).add_to(m)
            
            # Add label
            folium.Marker(
                location=[row['lat'], row['lng']],
                icon=folium.DivIcon(
                    html=f"""<div style='text-align: center; margin-top: -40px;'>
                            <b>{row['id']}</b>
                         </div>"""
                )
            ).add_to(m)
        
        # Display map
        st_folium(m, height=500, width=None, returned_objects=[])
    
    # Display stats cards
    with stats_placeholder.container():
        st.subheader("📊 Key Metrics")
        
        # Calculate stats
        neighbor_data = df[df['type'] != 'center']
        total_energy = neighbor_data['yearly_energy_kwh'].sum()
        total_carbon = neighbor_data['carbon_offset_kg'].sum() / 1000
        excellent_count = len(neighbor_data[neighbor_data['potential_category'] == 'Excellent'])
        good_count = len(neighbor_data[neighbor_data['potential_category'] == 'Good'])
        
        # Display metrics
        st.metric("Total Annual Energy", f"{total_energy:,.0f} kWh")
        st.metric("Carbon Offset", f"{total_carbon:.1f} tons/year")
        st.metric("Excellent Candidates", excellent_count)
        st.metric("Good Candidates", good_count)
    
    # Display chart
    with chart_placeholder.container():
        st.subheader("📈 Potential Distribution")
        
        # Create bar chart
        neighbor_data = df[df['type'] != 'center'].copy()
        neighbor_data['Home'] = neighbor_data['id'].astype(str)
        
        fig = px.bar(
            neighbor_data.sort_values('yearly_energy_kwh', ascending=False),
            x='Home',
            y='yearly_energy_kwh',
            color='potential_category',
            color_discrete_map={
                'Excellent': '#00ff00',
                'Good': '#90EE90',
                'Moderate': '#FFA500',
                'Low': '#FF6B6B'
            },
            title='Solar Potential by Home',
            labels={'yearly_energy_kwh': 'Annual Energy (kWh)'}
        )
        
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    # Display results table
    with results_placeholder.container():
        st.subheader("📋 Detailed Results")
        
        # Prepare display dataframe
        display_df = df[['id', 'label', 'icon', 'potential_category', 
                        'yearly_energy_kwh', 'roof_area_sqm', 'carbon_offset_kg']].copy()
        
        display_df.columns = ['ID', 'Location', 'Status', 'Potential', 
                             'Annual Energy (kWh)', 'Roof Area (sqm)', 'CO₂ Offset (kg)']
        
        # Format numbers
        display_df['Annual Energy (kWh)'] = display_df['Annual Energy (kWh)'].round(0).astype(int)
        display_df['Roof Area (sqm)'] = display_df['Roof Area (sqm)'].round(0).astype(int)
        display_df['CO₂ Offset (kg)'] = display_df['CO₂ Offset (kg)'].round(0).astype(int)
        
        st.dataframe(
            display_df,
            use_container_width=True,
            hide_index=True,
            column_config={
                "Status": st.column_config.TextColumn(width="small"),
                "ID": st.column_config.NumberColumn(width="small")
            }
        )
        
        # Download button
        csv = display_df.to_csv(index=False)
        st.download_button(
            label="📥 Download Results CSV",
            data=csv,
            file_name=f"solar_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
    
    # Display AI summary
    with summary_placeholder.container():
        st.markdown("---")
        st.subheader("🤖 AI-Generated Insights")
        st.markdown(st.session_state.ai_summary)

# Footer
st.markdown("---")
st.markdown(
    "<p style='text-align: center; color: gray;'>Built with ❤️ using Streamlit, Google APIs, and Gemini AI</p>",
    unsafe_allow_html=True
)