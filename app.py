import streamlit as st
import folium
from streamlit_folium import st_folium
import pandas as pd
import random
from datetime import datetime
import math
import plotly.express as px
import plotly.graph_objects as go
from utils.geocoding import geocode_address, generate_residential_addresses_around_point
from utils.grid_generator import generate_nearby_homes
from utils.solar_api import get_solar_potential, categorize_potential

# Page config
st.set_page_config(
    page_title="Solar Neighborhood Analyzer",
    page_icon="☀️",
    layout="wide"
)

st.title("☀️ Solar Neighborhood Analyzer")

# Custom CSS
st.markdown("""
<style>
    .stTabs [data-baseweb="tab-list"] {
        gap: 24px;
    }
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
    }
    div[data-testid="metric-container"] {
        background-color: rgba(28, 131, 225, 0.1);
        border: 1px solid rgba(28, 131, 225, 0.2);
        padding: 5px 10px;
        border-radius: 10px;
        margin: 5px 0;
    }
</style>
""", unsafe_allow_html=True)

# Realistic residential addresses for demo
DEMO_ADDRESSES = {
    "Beverly Hills, CA": {
        "center": {"lat": 34.0736, "lng": -118.4004, "address": "450 N Roxbury Dr, Beverly Hills, CA 90210"},
        "neighbors": [
            {"lat": 34.0739, "lng": -118.4008, "offset": "North", "address": "460 N Roxbury Dr"},
            {"lat": 34.0733, "lng": -118.4001, "offset": "South", "address": "440 N Roxbury Dr"},
            {"lat": 34.0738, "lng": -118.3998, "offset": "East", "address": "455 N Bedford Dr"},
            {"lat": 34.0734, "lng": -118.4010, "offset": "West", "address": "445 N Camden Dr"},
            {"lat": 34.0741, "lng": -118.4005, "offset": "Northeast", "address": "470 N Roxbury Dr"},
            {"lat": 34.0730, "lng": -118.4007, "offset": "Southwest", "address": "430 N Roxbury Dr"},
            {"lat": 34.0737, "lng": -118.3995, "offset": "Southeast", "address": "465 N Bedford Dr"},
            {"lat": 34.0735, "lng": -118.4013, "offset": "Northwest", "address": "435 N Camden Dr"}
        ]
    },
    "Palo Alto, CA": {
        "center": {"lat": 37.4419, "lng": -122.1430, "address": "2120 Middlefield Rd, Palo Alto, CA 94301"},
        "neighbors": [
            {"lat": 37.4422, "lng": -122.1428, "offset": "North", "address": "2130 Middlefield Rd"},
            {"lat": 37.4416, "lng": -122.1432, "offset": "South", "address": "2110 Middlefield Rd"},
            {"lat": 37.4420, "lng": -122.1425, "offset": "East", "address": "2125 Webster St"},
            {"lat": 37.4418, "lng": -122.1435, "offset": "West", "address": "2115 Cowper St"},
            {"lat": 37.4424, "lng": -122.1426, "offset": "Northeast", "address": "2140 Middlefield Rd"},
            {"lat": 37.4414, "lng": -122.1434, "offset": "Southwest", "address": "2100 Middlefield Rd"},
            {"lat": 37.4421, "lng": -122.1423, "offset": "Southeast", "address": "2135 Webster St"},
            {"lat": 37.4417, "lng": -122.1437, "offset": "Northwest", "address": "2105 Cowper St"}
        ]
    },
    "Austin, TX": {
        "center": {"lat": 30.2672, "lng": -97.7431, "address": "1500 S Congress Ave, Austin, TX 78704"},
        "neighbors": [
            {"lat": 30.2675, "lng": -97.7429, "offset": "North", "address": "1510 S Congress Ave"},
            {"lat": 30.2669, "lng": -97.7433, "offset": "South", "address": "1490 S Congress Ave"},
            {"lat": 30.2673, "lng": -97.7427, "offset": "East", "address": "1505 Travis Heights Blvd"},
            {"lat": 30.2671, "lng": -97.7435, "offset": "West", "address": "1495 S 1st St"},
            {"lat": 30.2676, "lng": -97.7426, "offset": "Northeast", "address": "1520 S Congress Ave"},
            {"lat": 30.2668, "lng": -97.7436, "offset": "Southwest", "address": "1480 S Congress Ave"},
            {"lat": 30.2674, "lng": -97.7424, "offset": "Southeast", "address": "1515 Travis Heights Blvd"},
            {"lat": 30.2670, "lng": -97.7438, "offset": "Northwest", "address": "1485 S 1st St"}
        ]
    }
}

# Sidebar
with st.sidebar:
    st.header("🏠 Configuration")
    
    # Location selector
    location = st.selectbox(
        "Select Demo Neighborhood",
        options=list(DEMO_ADDRESSES.keys()),
        help="Choose a real residential neighborhood"
    )
    
    custom_address = st.text_input(
        "Or enter custom address",
        placeholder="123 Main St, City, State",
        help="Enter any US residential address"
    )
    
    with st.expander("⚙️ Advanced Settings"):
        analysis_radius = st.slider(
            "Analysis radius (meters)",
            min_value=100,
            max_value=500,
            value=200,
            step=50,
            help="How far to search for homes"
        )
        
        map_style = st.selectbox(
            "Map Style",
            ["Satellite", "Hybrid", "Terrain", "Roadmap"],
            help="Choose map visualization style"
        )
    
    analyze_btn = st.button("🔍 Analyze Neighborhood", type="primary", use_container_width=True)

# Function to generate realistic solar data
def generate_realistic_solar_data(location_data):
    """Generate realistic solar data for actual houses using real solar API when possible"""
    homes_data = []

    # Add center home (with solar) - try to get real data
    center = location_data["center"]
    center_solar_data = get_solar_potential(center['lat'], center['lng'])

    if center_solar_data['success'] and center_solar_data['data_quality'] == 'actual':
        # Use real solar data
        homes_data.append({
            'id': 0,
            'label': 'Reference Solar Home',
            'lat': center['lat'],
            'lng': center['lng'],
            'address': center['address'],
            'type': 'center',
            'potential_category': 'Existing Solar',
            'yearly_energy_kwh': center_solar_data['yearly_energy_kwh'],
            'carbon_offset_kg': center_solar_data['carbon_offset_kg'],
            'roof_area_sqm': center_solar_data['roof_area_sqm'],
            'panel_capacity_kw': center_solar_data['panel_capacity_kw'],
            'installation_year': 2021,
            'system_efficiency': 0.92,
            'data_source': 'Google Solar API'
        })
    else:
        # Use simulated data as fallback
        homes_data.append({
            'id': 0,
            'label': 'Reference Solar Home',
            'lat': center['lat'],
            'lng': center['lng'],
            'address': center['address'],
            'type': 'center',
            'potential_category': 'Existing Solar',
            'yearly_energy_kwh': 7500,
            'carbon_offset_kg': 3750,
            'roof_area_sqm': 150,
            'panel_capacity_kw': 7.5,
            'installation_year': 2021,
            'system_efficiency': 0.92,
            'data_source': 'Simulated'
        })
    
    # Add neighboring homes with realistic variations
    for i, neighbor in enumerate(location_data["neighbors"]):
        # Try to get real solar data for this location
        neighbor_solar_data = get_solar_potential(neighbor['lat'], neighbor['lng'])

        if neighbor_solar_data['success'] and neighbor_solar_data['data_quality'] == 'actual':
            # Use real solar data from Google Solar API
            category_info = categorize_potential(neighbor_solar_data['yearly_energy_kwh'])

            homes_data.append({
                'id': i + 1,
                'label': f'Home {i + 1}',
                'lat': neighbor['lat'],
                'lng': neighbor['lng'],
                'address': neighbor['address'],
                'direction': neighbor['offset'],
                'type': 'neighbor',
                'potential_category': category_info['category'],
                'yearly_energy_kwh': round(neighbor_solar_data['yearly_energy_kwh']),
                'carbon_offset_kg': round(neighbor_solar_data['carbon_offset_kg']),
                'roof_area_sqm': round(neighbor_solar_data['roof_area_sqm']),
                'panel_capacity_kw': round(neighbor_solar_data['panel_capacity_kw'], 1),
                'roof_orientation': 'South',  # Default, as API doesn't provide this
                'shading_factor': 0.9,  # Default
                'roof_age': random.randint(5, 25),
                'data_source': 'Google Solar API'
            })
        else:
            # Fall back to simulated data with realistic variations
            roof_orientation = random.choice(['South', 'Southeast', 'Southwest', 'East', 'West'])
            shading_factor = random.uniform(0.7, 1.0)  # Trees, buildings, etc.
            roof_age = random.randint(5, 25)

            # Calculate potential based on realistic factors
            base_potential = 6000

            # Orientation affects potential
            orientation_multiplier = {
                'South': 1.0,
                'Southeast': 0.95,
                'Southwest': 0.95,
                'East': 0.85,
                'West': 0.85
            }

            potential = base_potential * orientation_multiplier[roof_orientation] * shading_factor

            # Add some randomness for roof size
            roof_area = random.randint(100, 250)
            potential = potential * (roof_area / 150)  # Normalize to average roof

            # Categorize
            if potential >= 6000:
                category = 'Excellent'
            elif potential >= 4000:
                category = 'Good'
            elif potential >= 2500:
                category = 'Moderate'
            else:
                category = 'Low'

            homes_data.append({
                'id': i + 1,
                'label': f'Home {i + 1}',
                'lat': neighbor['lat'],
                'lng': neighbor['lng'],
                'address': neighbor['address'],
                'direction': neighbor['offset'],
                'type': 'neighbor',
                'potential_category': category,
                'yearly_energy_kwh': round(potential),
                'carbon_offset_kg': round(potential * 0.5),
                'roof_area_sqm': roof_area,
                'panel_capacity_kw': round(potential / 1000, 1),
                'roof_orientation': roof_orientation,
                'shading_factor': round(shading_factor, 2),
                'roof_age': roof_age,
                'data_source': 'Simulated'
            })
    
    return pd.DataFrame(homes_data)

# Create enhanced Google-style map
def create_enhanced_map(df, map_style="Satellite"):
    """Create a professional map with satellite imagery"""
    
    center_home = df[df['type'] == 'center'].iloc[0]
    
    # Map tile configurations
    tile_configs = {
        "Satellite": "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}",
        "Hybrid": "https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}",
        "Terrain": "https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}",
        "Roadmap": "https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}"
    }
    
    # Create map
    m = folium.Map(
        location=[center_home['lat'], center_home['lng']],
        zoom_start=18,
        tiles=None,
        max_zoom=20,
        min_zoom=15
    )
    
    # Add selected tile layer
    folium.TileLayer(
        tiles=tile_configs[map_style],
        attr=f'Google {map_style}',
        name=f'Google {map_style}',
        overlay=False,
        control=True,
        show=True
    ).add_to(m)
    
    # Add markers for each home
    for _, home in df.iterrows():
        # Color scheme
        if home['type'] == 'center':
            color = '#0066CC'
            icon = '🏠'
            size = 40
        else:
            colors = {
                'Excellent': '#00ff00',
                'Good': '#90EE90',
                'Moderate': '#FFA500',
                'Low': '#FF6B6B'
            }
            color = colors.get(home['potential_category'], '#808080')
            icon = {
                'Excellent': '🟢',
                'Good': '🟡', 
                'Moderate': '🟠',
                'Low': '🔴'
            }.get(home['potential_category'], '⚫')
            size = 35
        
        # Create detailed popup
        if home['type'] == 'center':
            popup_html = f"""
            <div style="font-family: Arial; width: 250px;">
                <h4 style="margin: 0; color: #0066CC;">{icon} {home['label']}</h4>
                <hr style="margin: 5px 0;">
                <b>Address:</b> {home['address']}<br>
                <b>Status:</b> Solar Installed ({home['installation_year']})<br>
                <b>System Size:</b> {home['panel_capacity_kw']} kW<br>
                <b>Annual Generation:</b> {home['yearly_energy_kwh']:,} kWh<br>
                <b>System Efficiency:</b> {home['system_efficiency']:.0%}
            </div>
            """
        else:
            popup_html = f"""
            <div style="font-family: Arial; width: 250px;">
                <h4 style="margin: 0; color: {color};">{icon} {home['label']} ({home['direction']})</h4>
                <hr style="margin: 5px 0;">
                <b>Address:</b> {home['address']}<br>
                <b>Solar Potential:</b> {home['potential_category']}<br>
                <b>Annual Energy:</b> {home['yearly_energy_kwh']:,} kWh<br>
                <b>CO₂ Offset:</b> {home['carbon_offset_kg']/1000:.1f} tons/yr<br>
                <b>Roof Area:</b> {home['roof_area_sqm']} m²<br>
                <b>Roof Facing:</b> {home.get('roof_orientation', 'N/A')}<br>
                <b>Shading Factor:</b> {home.get('shading_factor', 0):.0%}<br>
                <b>Roof Age:</b> {home.get('roof_age', 'N/A')} years
            </div>
            """
        
        # Custom marker icon
        icon_html = f"""
        <div style="
            background-color: {color};
            width: {size}px;
            height: {size}px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        ">
            {icon}
        </div>
        """
        
        folium.Marker(
            location=[home['lat'], home['lng']],
            popup=folium.Popup(popup_html, max_width=300),
            tooltip=f"{home['label']}: {home.get('potential_category', 'N/A')}",
            icon=folium.DivIcon(html=icon_html)
        ).add_to(m)
    
    # Add analysis radius circle
    folium.Circle(
        location=[center_home['lat'], center_home['lng']],
        radius=analysis_radius,
        color='white',
        weight=2,
        fill=True,
        fillColor='blue',
        fillOpacity=0.1,
        popup=f'Analysis Radius: {analysis_radius}m',
        dash_array='10'
    ).add_to(m)
    
    return m

# Main analysis logic
if analyze_btn:
    # Use custom address if provided, otherwise use selected location
    if custom_address:
        st.info(f"Analyzing custom address: {custom_address}")



        # Geocode the custom address
        with st.spinner("🌍 Geocoding address..."):
            geocode_result = geocode_address(custom_address)

            if geocode_result['success']:
                # Generate realistic nearby residential addresses
                with st.spinner("🏠 Finding nearby addresses..."):
                    nearby_addresses = generate_residential_addresses_around_point(
                        geocode_result['lat'],
                        geocode_result['lng'],
                        radius_meters=analysis_radius,
                        num_addresses=8
                    )

                # Create location_data structure similar to DEMO_ADDRESSES
                location_data = {
                    "center": {
                        "lat": geocode_result['lat'],
                        "lng": geocode_result['lng'],
                        "address": geocode_result['formatted_address']
                    },
                    "neighbors": []
                }

                # Add neighbors from realistic addresses
                for addr in nearby_addresses:
                    # Determine direction based on relative position
                    lat_diff = addr['lat'] - geocode_result['lat']
                    lng_diff = addr['lng'] - geocode_result['lng']

                    if abs(lat_diff) > abs(lng_diff):
                        direction = "North" if lat_diff > 0 else "South"
                    else:
                        direction = "East" if lng_diff > 0 else "West"

                    location_data["neighbors"].append({
                        "lat": addr['lat'],
                        "lng": addr['lng'],
                        "offset": direction,
                        "address": addr['address']
                    })

                st.success(f"✅ Found location: {geocode_result['formatted_address']}")
                st.info(f"📍 Generated {len(nearby_addresses)} nearby addresses within {analysis_radius}m")

            else:
                st.error(f"❌ Geocoding failed: {geocode_result['error']}")
                st.info("Using demo location instead...")
                location_data = DEMO_ADDRESSES[location]
    else:
        location_data = DEMO_ADDRESSES[location]

    # Store location_data in session state for later use
    st.session_state.location_data = location_data
    
    with st.spinner("🔄 Analyzing neighborhood solar potential..."):
        # Simulate processing steps
        progress_bar = st.progress(0)
        status = st.empty()
        
        status.text("📍 Identifying neighborhood homes...")
        progress_bar.progress(25)
        
        # Generate data
        df = generate_realistic_solar_data(location_data)
        st.session_state.df = df
        
        status.text("☀️ Calculating solar potential using Google Solar API...")
        progress_bar.progress(50)
        
        status.text("🗺️ Creating visualization...")
        progress_bar.progress(75)
        
        status.text("✅ Analysis complete!")
        progress_bar.progress(100)
        
        import time
        time.sleep(0.5)
        progress_bar.empty()
        status.empty()

# Display results
if 'df' in st.session_state:
    df = st.session_state.df
    
    # Create tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🗺️ Map View", "📊 Analytics", "📋 Data Table", "💾 Export"])
    
    with tab1:
        col1, col2 = st.columns([3, 1])
        
        with col1:
            st.subheader("🛰️ Neighborhood Solar Potential Map")
            
            # Create and display map
            m = create_enhanced_map(df, map_style)
            map_data = st_folium(m, height=600, width=None, key="solar_map")

            # Add property ranking list below the map
            st.subheader("🏆 Property Solar Potential Ranking")

            neighbors = df[df['type'] != 'center'].copy()
            neighbors_sorted = neighbors.sort_values('yearly_energy_kwh', ascending=False)

            # Create ranking with color coding
            for idx, (_, home) in enumerate(neighbors_sorted.iterrows(), 1):
                potential = home['yearly_energy_kwh']
                category = home['potential_category']

                # Color coding based on potential
                if category == 'Excellent':
                    color = "🟢"
                    status = "BEST"
                elif category == 'Good':
                    color = "🟡"
                    status = "GOOD"
                elif category == 'Moderate':
                    color = "🟠"
                    status = "MODERATE"
                else:
                    color = "🔴"
                    status = "LOW"

                # Create expandable section for each property
                with st.expander(f"{color} #{idx} - {home['address']} ({status} - {potential:,.0f} kWh/yr)"):
                    col_a, col_b, col_c = st.columns(3)

                    with col_a:
                        st.metric("Annual Generation", f"{potential:,.0f} kWh")
                        st.metric("Roof Area", f"{home['roof_area_sqm']:.0f} m²")

                    with col_b:
                        st.metric("CO₂ Offset", f"{home['carbon_offset_kg']/1000:.1f} tons/yr")
                        st.metric("Roof Orientation", home.get('roof_orientation', 'N/A'))

                    with col_c:
                        st.metric("Est. Annual Savings", f"${potential * 0.15:,.0f}")
                        if category in ['Excellent', 'Good']:
                            st.success("✅ Recommended for Solar")
                        else:
                            st.info("ℹ️ Consider other factors")
        
        with col2:
            st.subheader("🎯 Map Legend")
            
            legend_items = [
                ("🏠 Blue", "Your Solar Home"),
                ("🟢 Green", "Excellent (>6000 kWh/yr)"),
                ("🟡 Yellow", "Good (4000-6000 kWh/yr)"),
                ("🟠 Orange", "Moderate (2500-4000 kWh/yr)"),
                ("🔴 Red", "Low (<2500 kWh/yr)")
            ]
            
            for icon, desc in legend_items:
                st.markdown(f"{icon} **{desc}**")
            
            st.markdown("---")
            
            # Quick stats
            st.subheader("📊 Quick Stats")
            neighbors = df[df['type'] != 'center']
            
            # Fixed metrics
            col_a, col_b = st.columns(2)
            with col_a:
                high_potential = len(neighbors[neighbors['potential_category'].isin(['Excellent', 'Good'])])
                st.metric(
                    label="🏆 Top Candidates",
                    value=f"{high_potential}/{len(neighbors)}"
                )
            
            with col_b:
                avg_potential = neighbors['yearly_energy_kwh'].mean()
                st.metric(
                    label="⚡ Avg. Potential",
                    value=f"{avg_potential:.0f} kWh/yr"
                )
            
            # Fixed CO2 metric
            total_co2 = neighbors['carbon_offset_kg'].sum()/1000
            st.metric(
                label="🌍 Total CO₂ Offset",
                value=f"{total_co2:.1f} tons/yr",
                help="If all neighbors install solar"
            )
            
            # Best candidate
            st.markdown("---")
            st.subheader("🥇 Best Candidate")
            top_home = neighbors.nlargest(1, 'yearly_energy_kwh').iloc[0]
            st.success(f"**{top_home['address']}**  \n{top_home['yearly_energy_kwh']:,} kWh/year  \nRoof: {top_home['roof_orientation']} facing")
    
    with tab2:
        st.subheader("📊 Solar Potential Analytics")
        
        neighbors = df[df['type'] != 'center']
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Potential distribution
            potential_counts = neighbors['potential_category'].value_counts()
            
            fig_pie = go.Figure(data=[go.Pie(
                labels=potential_counts.index,
                values=potential_counts.values,
                hole=0.4,
                marker_colors=['#00ff00', '#90EE90', '#FFA500', '#FF6B6B'],
                textinfo='label+percent'
            )])
            
            fig_pie.update_layout(
                title="Solar Potential Distribution",
                height=400,
                annotations=[dict(text='Homes', x=0.5, y=0.5, font_size=20, showarrow=False)]
            )
            
            st.plotly_chart(fig_pie, use_container_width=True)
            
            # Roof orientation analysis
            st.subheader("🧭 Roof Orientation Impact")
            orientation_data = neighbors.groupby('roof_orientation')['yearly_energy_kwh'].mean().sort_values(ascending=False)
            
            fig_orientation = px.bar(
                x=orientation_data.index,
                y=orientation_data.values,
                labels={'x': 'Roof Orientation', 'y': 'Avg. Energy (kWh/yr)'},
                color=orientation_data.values,
                color_continuous_scale='Viridis'
            )
            st.plotly_chart(fig_orientation, use_container_width=True)
        
        with col2:
            # Energy potential by home
            sorted_neighbors = neighbors.sort_values('yearly_energy_kwh', ascending=False)
            
            fig_bar = px.bar(
                sorted_neighbors,
                x='yearly_energy_kwh',
                y='address',
                orientation='h',
                color='potential_category',
                color_discrete_map={
                    'Excellent': '#00ff00',
                    'Good': '#90EE90',
                    'Moderate': '#FFA500',
                    'Low': '#FF6B6B'
                },
                labels={'yearly_energy_kwh': 'Annual Energy (kWh)', 'address': 'Address'},
                title="Energy Generation Potential by Home"
            )
            
            fig_bar.update_layout(height=600, showlegend=False)
            st.plotly_chart(fig_bar, use_container_width=True)
        
        # Impact metrics
        st.subheader("🌟 Neighborhood Impact Analysis")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_energy = neighbors['yearly_energy_kwh'].sum()
            st.info(f"""
            **⚡ Total Energy**  
            {total_energy:,} kWh/yr  
            Powers ~{total_energy/11000:.0f} homes
            """)
        
        with col2:
            total_carbon = neighbors['carbon_offset_kg'].sum()/1000
            st.success(f"""
            **🌱 CO₂ Reduction**  
            {total_carbon:.1f} tons/yr  
            = {total_carbon*24:.0f} trees
            """)
        
        with col3:
            total_panels = neighbors['panel_capacity_kw'].sum()
            st.warning(f"""
            **☀️ Solar Capacity**  
            {total_panels:.1f} kW  
            ~{total_panels*4:.0f} panels
            """)
        
        with col4:
            avg_roi = 7.5  # years average
            st.error(f"""
            **💰 Avg. ROI**  
            {avg_roi} years  
            25 yr savings: ${total_energy*0.15*25:,.0f}
            """)
    
    with tab3:
        st.subheader("📋 Detailed Solar Analysis Data")
        
        # Prepare display dataframe
        display_df = df.copy()
        
        # Select and order columns
        display_columns = [
            'address', 'direction', 'potential_category', 'yearly_energy_kwh',
            'carbon_offset_kg', 'roof_area_sqm', 'roof_orientation',
            'shading_factor', 'roof_age', 'data_source'
        ]
        
        # Filter columns that exist
        available_columns = [col for col in display_columns if col in display_df.columns]
        display_df = display_df[available_columns]
        
        # Style function
        def style_row(row):
            if 'potential_category' in row:
                if row['potential_category'] == 'Existing Solar':
                    return ['background-color: #e6f3ff'] * len(row)
                elif row['potential_category'] == 'Excellent':
                    return ['background-color: #d4f1d4'] * len(row)
                elif row['potential_category'] == 'Good':
                    return ['background-color: #e8f5e8'] * len(row)
                elif row['potential_category'] == 'Moderate':
                    return ['background-color: #fff4e6'] * len(row)
                else:
                    return ['background-color: #ffe6e6'] * len(row)
            return [''] * len(row)
        
        st.dataframe(
            display_df.style.apply(style_row, axis=1),
            use_container_width=True,
            height=500
        )
        
        # Summary stats
        st.subheader("📊 Statistical Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("**Top 3 Homes by Potential**")
            top3 = neighbors.nlargest(3, 'yearly_energy_kwh')[['address', 'yearly_energy_kwh']]
            st.dataframe(top3)
        
        with col2:
            st.markdown("**Potential by Orientation**")
            orientation_summary = neighbors.groupby('roof_orientation')['yearly_energy_kwh'].agg(['mean', 'count'])
            st.dataframe(orientation_summary)
        
        with col3:
            st.markdown("**Category Distribution**")
            category_summary = neighbors['potential_category'].value_counts()
            st.dataframe(category_summary)
    with tab4:
        st.subheader("💾 Export Results & Summary")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### 🤖 AI-Generated Executive Summary")
            
            # Calculate summary statistics
            neighbors = df[df['type'] != 'center']
            excellent = len(neighbors[neighbors['potential_category'] == 'Excellent'])
            good = len(neighbors[neighbors['potential_category'] == 'Good'])
            total_energy = neighbors['yearly_energy_kwh'].sum()
            total_carbon = neighbors['carbon_offset_kg'].sum()/1000
            best_homes = neighbors.nlargest(3, 'yearly_energy_kwh')
            
            summary = f"""
            📊 **Solar Neighborhood Analysis Report**
            
            **Location:** {st.session_state.get('location_data', {}).get('center', {}).get('address', 'Unknown Location')}
            **Analysis Date:** {datetime.now().strftime('%B %d, %Y')}  
            **Homes Analyzed:** {len(neighbors)} properties within {analysis_radius}m
            
            **🎯 Key Findings:**
            
            This comprehensive analysis of the {location} neighborhood reveals significant 
            solar adoption potential. Of the {len(neighbors)} homes analyzed:
            
            • **{excellent}** properties show EXCELLENT solar potential (>6,000 kWh/year)
            • **{good}** properties show GOOD potential (4,000-6,000 kWh/year)
            • **{len(neighbors) - excellent - good}** properties have moderate to low potential
            
            **💡 Top 3 Solar Candidates:**
            """
            
            for idx, (_, home) in enumerate(best_homes.iterrows(), 1):
                summary += f"""
            {idx}. **{home['address']}**
               - Annual Generation: {home['yearly_energy_kwh']:,} kWh
               - CO₂ Offset: {home['carbon_offset_kg']/1000:.1f} tons/year
               - Roof: {home['roof_area_sqm']}m², facing {home.get('roof_orientation', 'N/A')}
            """
            
            summary += f"""
            
            **🌍 Environmental Impact:**
            If all viable homes ({excellent + good} properties) install solar:
            - Total Energy Generation: **{total_energy:,.0f} kWh/year**
            - Carbon Offset: **{total_carbon:.1f} tons CO₂/year**
            - Equivalent to planting **{int(total_carbon * 24)} trees annually**
            
            **💰 Economic Benefits:**
            - Estimated Annual Savings: **${total_energy * 0.15:,.0f}**
            - 25-Year Savings: **${total_energy * 0.15 * 25:,.0f}**
            - Average Payback Period: **7-8 years**
            
            **📈 Recommendations:**
            1. **Immediate Action:** Contact the {excellent} excellent-rated homes first
            2. **Community Approach:** Organize a neighborhood solar information session
            3. **Group Buying:** Leverage bulk purchasing for 10-15% cost reduction
            4. **Success Story:** Use the existing solar home as a case study
            
            **🔮 Next Steps:**
            - Schedule consultations with top candidates
            - Arrange site assessments for shading analysis
            - Explore local incentives and tax credits
            - Consider community solar options for lower-potential homes
            
            ---
            *Analysis based on roof characteristics, orientation, shading, and local solar irradiance data*
            """
            
            st.info(summary)
            
            # Download summary
            st.download_button(
                label="📄 Download Summary (TXT)",
                data=summary,
                file_name=f"solar_analysis_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.txt",
                mime="text/plain"
            )
        
        with col2:
            st.markdown("### 📥 Data Export Options")
            
            # Prepare full export data
            export_df = df.copy()
            export_df['analysis_date'] = datetime.now().strftime('%Y-%m-%d')
            export_df['analysis_location'] = location
            export_df['analysis_radius_m'] = analysis_radius
            
            # CSV export
            csv = export_df.to_csv(index=False)
            st.download_button(
                label="📊 Download Full Data (CSV)",
                data=csv,
                file_name=f"solar_data_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.csv",
                mime="text/csv"
            )
            
            # Create professional HTML report
            html_report = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Solar Neighborhood Analysis - {location}</title>
                <style>
                    body {{ 
                        font-family: 'Segoe UI', Arial, sans-serif; 
                        margin: 40px;
                        color: #333;
                    }}
                    h1 {{ 
                        color: #FF6B6B; 
                        border-bottom: 3px solid #FF6B6B;
                        padding-bottom: 10px;
                    }}
                    h2 {{ 
                        color: #333; 
                        margin-top: 30px;
                    }}
                    table {{ 
                        border-collapse: collapse; 
                        width: 100%; 
                        margin: 20px 0;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }}
                    th, td {{ 
                        border: 1px solid #ddd; 
                        padding: 12px; 
                        text-align: left; 
                    }}
                    th {{ 
                        background-color: #FF6B6B; 
                        color: white;
                        font-weight: bold;
                    }}
                    tr:nth-child(even) {{ background-color: #f9f9f9; }}
                    tr:hover {{ background-color: #f5f5f5; }}
                    .excellent {{ background-color: #d4f1d4 !important; }}
                    .good {{ background-color: #e8f5e8 !important; }}
                    .moderate {{ background-color: #fff4e6 !important; }}
                    .low {{ background-color: #ffe6e6 !important; }}
                    .metric-box {{
                        display: inline-block;
                        padding: 20px;
                        margin: 10px;
                        border-radius: 10px;
                        background-color: #f0f2f6;
                        text-align: center;
                        min-width: 150px;
                    }}
                    .metric-value {{
                        font-size: 2em;
                        font-weight: bold;
                        color: #FF6B6B;
                    }}
                    .metric-label {{
                        font-size: 0.9em;
                        color: #666;
                        margin-top: 5px;
                    }}
                </style>
            </head>
            <body>
                <h1>☀️ Solar Neighborhood Analysis Report</h1>
                <p><strong>Location:</strong> {st.session_state.get('location_data', {}).get('center', {}).get('address', 'Unknown Location')}</p>
                <p><strong>Report Date:</strong> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</p>
                <p><strong>Analysis Radius:</strong> {analysis_radius} meters</p>
                
                <h2>📊 Summary Metrics</h2>
                <div>
                    <div class="metric-box">
                        <div class="metric-value">{len(neighbors)}</div>
                        <div class="metric-label">Homes Analyzed</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{excellent + good}</div>
                        <div class="metric-label">High Potential</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{total_energy/1000:.0f}</div>
                        <div class="metric-label">MWh/Year Potential</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value">{total_carbon:.0f}</div>
                        <div class="metric-label">Tons CO₂ Offset</div>
                    </div>
                </div>
                
                <h2>🏠 Detailed Home Analysis</h2>
                <table>
                    <tr>
                        <th>Address</th>
                        <th>Direction</th>
                        <th>Potential</th>
                        <th>Annual Energy (kWh)</th>
                        <th>CO₂ Offset (tons/yr)</th>
                        <th>Roof Area (m²)</th>
                        <th>Orientation</th>
                        <th>Shading</th>
                    </tr>
            """
            
            for _, home in df.iterrows():
                row_class = home['potential_category'].lower().replace(' ', '')
                html_report += f"""
                    <tr class="{row_class}">
                        <td>{home['address']}</td>
                        <td>{home.get('direction', 'Center')}</td>
                        <td><strong>{home['potential_category']}</strong></td>
                        <td>{home['yearly_energy_kwh']:,}</td>
                        <td>{home['carbon_offset_kg']/1000:.1f}</td>
                        <td>{home['roof_area_sqm']}</td>
                        <td>{home.get('roof_orientation', 'N/A')}</td>
                        <td>{home.get('shading_factor', 0):.0%}</td>
                    </tr>
                """
            
            html_report += """
                </table>
                
                <h2>💡 Recommendations</h2>
                <ol>
                    <li>Begin with homes rated "Excellent" for highest impact</li>
                    <li>Organize a community information session</li>
                    <li>Explore group purchasing options</li>
                    <li>Investigate local and federal incentives</li>
                    <li>Consider battery storage for enhanced benefits</li>
                </ol>
                
                <p style="margin-top: 40px; text-align: center; color: #666;">
                    Generated by Solar Neighborhood Analyzer | Powered by Google Maps & AI
                </p>
            </body>
            </html>
            """
            
            st.download_button(
                label="📑 Download Report (HTML)",
                data=html_report,
                file_name=f"solar_report_{location.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.html",
                mime="text/html"
            )
            
            # Quick metrics
            st.markdown("### 📈 Quick Metrics")
            
            metric1, metric2 = st.columns(2)
            with metric1:
                st.metric("Best Home", f"{best_homes.iloc[0]['yearly_energy_kwh']:,} kWh/yr")
            with metric2:
                st.metric("Avg Potential", f"{neighbors['yearly_energy_kwh'].mean():,.0f} kWh/yr")
            
            # Share options
            st.markdown("### 🔗 Share Results")
            
            share_text = f"""🌟 Solar Analysis Complete for {location}!
Found {excellent + good} high-potential homes out of {len(neighbors)} analyzed.
Total potential: {total_energy:,.0f} kWh/year
Carbon offset: {total_carbon:.1f} tons CO₂/year
#SolarEnergy #Sustainability #CleanEnergy"""
            
            st.text_area("Copy this text to share:", share_text, height=100)
            
            # ROI Calculator
            st.markdown("### 💰 Quick ROI Calculator")
            
            system_cost = st.number_input(
                "Average system cost ($)",
                min_value=10000,
                max_value=50000,
                value=20000,
                step=1000
            )
            
            electricity_rate = st.number_input(
                "Electricity rate ($/kWh)",
                min_value=0.05,
                max_value=0.50,
                value=0.15,
                step=0.01
            )
            
            if st.button("Calculate ROI"):
                avg_energy = neighbors['yearly_energy_kwh'].mean()
                annual_savings = avg_energy * electricity_rate
                payback_years = system_cost / annual_savings
                total_savings = annual_savings * 25 - system_cost
                
                st.success(f"""
                **ROI Analysis (Average Home):**
                - Annual Savings: ${annual_savings:,.0f}
                - Payback Period: {payback_years:.1f} years
                - 25-Year Net Savings: ${total_savings:,.0f}
                - ROI: {(total_savings / system_cost * 100):.0f}%
                """)

# Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; padding: 20px;'>
        <p>☀️ Solar Neighborhood Analyzer | Real Addresses, Real Potential | 
        Built for a Sustainable Future</p>
        <p style='font-size: 0.9em;'>Data accuracy depends on roof conditions, local weather patterns, and shading analysis</p>
    </div>
    """,
    unsafe_allow_html=True
)

# Add session state persistence for better UX
if 'analyzed_locations' not in st.session_state:
    st.session_state.analyzed_locations = []

if analyze_btn and location not in st.session_state.analyzed_locations:
    st.session_state.analyzed_locations.append(location)

# Optional: Add comparison feature
if len(st.session_state.analyzed_locations) > 1:
    with st.expander("📊 Compare Previous Analyses"):
        st.write("Previously analyzed locations:")
        for loc in st.session_state.analyzed_locations:
            st.write(f"- {loc}")