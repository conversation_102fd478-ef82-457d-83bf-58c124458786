Metadata-Version: 2.2
Name: pyproj
Version: 3.7.1
Summary: Python interface to <PERSON>O<PERSON> (cartographic projections and coordinate transformations library)
Home-page: https://github.com/pyproj4/pyproj
Author-email: <PERSON> <jef<PERSON>.<PERSON>.<EMAIL>>
Maintainer: pyproj contributors
License: MIT
Project-URL: homepage, https://pyproj4.github.io/pyproj/
Project-URL: documentation, https://pyproj4.github.io/pyproj/
Project-URL: repository, https://github.com/pyproj4/pyproj
Project-URL: changelog, https://pyproj4.github.io/pyproj/stable/history.html
Keywords: GIS,map,geospatial,coordinate-systems,coordinate-transformation,cartographic-projection,geodesic
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: GIS
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.10
Description-Content-Type: text/markdown
License-File: LICENSE
License-File: LICENSE_proj
Requires-Dist: certifi
Dynamic: home-page

![Pyproj logo](https://raw.githubusercontent.com/pyproj4/pyproj/main/docs/media/logo.png)

# pyproj

Python interface to [PROJ](http://proj.org) (cartographic projections and coordinate transformations library).

<p align="center">
<a href="#contributors"><img alt="All Contributors" src="https://img.shields.io/badge/all_contributors-66-orange.svg?style=flat-square"></a>
<a href="https://github.com/pyproj4/pyproj/actions?query=workflow%3ATests"><img alt="GitHub Actions Build Status" src="https://github.com/pyproj4/pyproj/workflows/Tests/badge.svg"></a>
<a href="https://codecov.io/gh/pyproj4/pyproj"><img alt="Codecov Status" src="https://codecov.io/gh/pyproj4/pyproj/branch/main/graph/badge.svg"></a>
<a href="https://badge.fury.io/py/pyproj"><img alt="PyPI" src="https://badge.fury.io/py/pyproj.svg"></a>
<a href="https://pepy.tech/project/pyproj"><img alt="Downloads" src="https://pepy.tech/badge/pyproj"></a>
<a href="https://anaconda.org/conda-forge/pyproj"><img alt="Anaconda-Server Badge" src="https://anaconda.org/conda-forge/pyproj/badges/version.svg"></a>
<a href="https://github.com/python/black"><img alt="Code style: black" src="https://img.shields.io/badge/code%20style-black-000000.svg"></a>
<a href="https://github.com/pre-commit/pre-commit"><img alt="pre-commit" src="https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white"></a>
<a href="https://zenodo.org/badge/latestdoi/28607354"><img alt="DOI" src="https://zenodo.org/badge/28607354.svg"></a>
</p>


## Documentation

- Stable: http://pyproj4.github.io/pyproj/stable/
- Latest: https://pyproj4.github.io/pyproj/latest/

## Bugs/Questions

- Report bugs/feature requests: https://github.com/pyproj4/pyproj/issues
- Ask questions: https://github.com/pyproj4/pyproj/discussions
- Ask the GIS community: https://gis.stackexchange.com/questions/tagged/pyproj

## Contributors ✨

Thanks goes to these wonderful people ([emoji key](https://allcontributors.org/docs/en/emoji-key)):

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore-start -->
<!-- markdownlint-disable -->
<table>
  <tr>
    <td align="center"><a href="https://github.com/jswhit"><img src="https://avatars2.githubusercontent.com/u/579593?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Jeff Whitaker</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=jswhit" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jswhit" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jswhit" title="Code">💻</a> <a href="#example-jswhit" title="Examples">💡</a> <a href="#ideas-jswhit" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/pyproj4/pyproj/pulls?q=is%3Apr+reviewed-by%3Ajswhit" title="Reviewed Pull Requests">👀</a> <a href="#question-jswhit" title="Answering Questions">💬</a> <a href="#maintenance-jswhit" title="Maintenance">🚧</a> <a href="#infra-jswhit" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ajswhit" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://github.com/snowman2"><img src="https://avatars3.githubusercontent.com/u/8699967?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Alan D. Snow</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=snowman2" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=snowman2" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/commits?author=snowman2" title="Code">💻</a> <a href="#example-snowman2" title="Examples">💡</a> <a href="#maintenance-snowman2" title="Maintenance">🚧</a> <a href="#infra-snowman2" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="#ideas-snowman2" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/pyproj4/pyproj/pulls?q=is%3Apr+reviewed-by%3Asnowman2" title="Reviewed Pull Requests">👀</a> <a href="#question-snowman2" title="Answering Questions">💬</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Asnowman2" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://github.com/micahcochran"><img src="https://avatars0.githubusercontent.com/u/7433104?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Micah Cochran</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=micahcochran" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=micahcochran" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/commits?author=micahcochran" title="Code">💻</a> <a href="#maintenance-micahcochran" title="Maintenance">🚧</a> <a href="#infra-micahcochran" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/pyproj4/pyproj/pulls?q=is%3Apr+reviewed-by%3Amicahcochran" title="Reviewed Pull Requests">👀</a> <a href="#question-micahcochran" title="Answering Questions">💬</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Amicahcochran" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://jorisvandenbossche.github.io/"><img src="https://avatars2.githubusercontent.com/u/1020496?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Joris Van den Bossche</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=jorisvandenbossche" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jorisvandenbossche" title="Code">💻</a> <a href="#ideas-jorisvandenbossche" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/pyproj4/pyproj/pulls?q=is%3Apr+reviewed-by%3Ajorisvandenbossche" title="Reviewed Pull Requests">👀</a> <a href="#question-jorisvandenbossche" title="Answering Questions">💬</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ajorisvandenbossche" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jorisvandenbossche" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/cjmayo"><img src="https://avatars1.githubusercontent.com/u/921089?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Chris Mayo</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=cjmayo" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://www.petrel.org"><img src="https://avatars1.githubusercontent.com/u/2298266?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Charles Karney</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=cffk" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=cffk" title="Tests">⚠️</a></td>
    <td align="center"><a href="http://www.justaprogrammer.net/profile/justin"><img src="https://avatars3.githubusercontent.com/u/146930?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Justin Dearing</b></sub></a><br /><a href="#infra-zippy1981" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/jdkloe"><img src="https://avatars3.githubusercontent.com/u/1906112?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Jos de Kloe</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=jdkloe" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jdkloe" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ajdkloe" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://github.com/georgeouzou"><img src="https://avatars3.githubusercontent.com/u/16732042?v=4?s=100" width="100px;" alt=""/><br /><sub><b>George Ouzounoudis</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=georgeouzou" title="Code">💻</a> <a href="#ideas-georgeouzou" title="Ideas, Planning, & Feedback">🤔</a></td>
    <td align="center"><a href="https://github.com/djhoese"><img src="https://avatars3.githubusercontent.com/u/1828519?v=4?s=100" width="100px;" alt=""/><br /><sub><b>David Hoese</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/pulls?q=is%3Apr+reviewed-by%3Adjhoese" title="Reviewed Pull Requests">👀</a> <a href="#ideas-djhoese" title="Ideas, Planning, & Feedback">🤔</a> <a href="#platform-djhoese" title="Packaging/porting to new platform">📦</a> <a href="https://github.com/pyproj4/pyproj/commits?author=djhoese" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=djhoese" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/commits?author=djhoese" title="Code">💻</a></td>
    <td align="center"><a href="http://mitkin.github.io"><img src="https://avatars3.githubusercontent.com/u/3927849?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Mikhail Itkin</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=mitkin" title="Code">💻</a></td>
    <td align="center"><a href="http://dopplershift.github.io"><img src="https://avatars2.githubusercontent.com/u/221526?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Ryan May</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=dopplershift" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/artttt"><img src="https://avatars3.githubusercontent.com/u/4626281?v=4?s=100" width="100px;" alt=""/><br /><sub><b>artttt</b></sub></a><br /><a href="#ideas-artttt" title="Ideas, Planning, & Feedback">🤔</a></td>
    <td align="center"><a href="http://ocefpaf.github.io/python4oceanographers"><img src="https://avatars1.githubusercontent.com/u/950575?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Filipe</b></sub></a><br /><a href="#infra-ocefpaf" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="https://github.com/pyproj4/pyproj/commits?author=ocefpaf" title="Code">💻</a> <a href="#platform-ocefpaf" title="Packaging/porting to new platform">📦</a> <a href="https://github.com/pyproj4/pyproj/commits?author=ocefpaf" title="Documentation">📖</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/heitorPB"><img src="https://avatars2.githubusercontent.com/u/13461702?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Heitor</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=heitorPB" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/sebastic"><img src="https://avatars3.githubusercontent.com/u/4605306?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Bas Couwenberg</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=sebastic" title="Code">💻</a> <a href="#platform-sebastic" title="Packaging/porting to new platform">📦</a> <a href="https://github.com/pyproj4/pyproj/commits?author=sebastic" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/nickeubank"><img src="https://avatars0.githubusercontent.com/u/9683693?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Nick Eubank</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=nickeubank" title="Code">💻</a></td>
    <td align="center"><a href="https://www.math.uwaterloo.ca/~mdunphy/"><img src="https://avatars3.githubusercontent.com/u/9088426?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Michael Dunphy</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=mdunphy" title="Documentation">📖</a></td>
    <td align="center"><a href="http://matthew.dynevor.org"><img src="https://avatars2.githubusercontent.com/u/67612?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Matthew Brett</b></sub></a><br /><a href="#infra-matthew-brett" title="Infrastructure (Hosting, Build-Tools, etc)">🚇</a> <a href="#platform-matthew-brett" title="Packaging/porting to new platform">📦</a></td>
    <td align="center"><a href="https://naboa.de"><img src="https://avatars1.githubusercontent.com/u/********?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Jakob de Maeyer </b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=jdemaeyer" title="Code">💻</a></td>
    <td align="center"><a href="https://gitter.im"><img src="https://avatars2.githubusercontent.com/u/8518239?v=4?s=100" width="100px;" alt=""/><br /><sub><b>The Gitter Badger</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=gitter-badger" title="Documentation">📖</a></td>
  </tr>
  <tr>
    <td align="center"><a href="http://lizards.opensuse.org/author/bmwiedemann/"><img src="https://avatars3.githubusercontent.com/u/637990?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Bernhard M. Wiedemann</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=bmwiedemann" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/ReallyNiceGuy"><img src="https://avatars0.githubusercontent.com/u/6545730?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Marco Aurélio da Costa</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=ReallyNiceGuy" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/ChrisBarker-NOAA"><img src="https://avatars2.githubusercontent.com/u/916576?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Christopher H. Barker</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=ChrisBarker-NOAA" title="Code">💻</a></td>
    <td align="center"><a href="https://evers.dev/"><img src="https://avatars3.githubusercontent.com/u/13132571?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Kristian Evers</b></sub></a><br /><a href="#question-kbevers" title="Answering Questions">💬</a> <a href="#ideas-kbevers" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/pyproj4/pyproj/commits?author=kbevers" title="Documentation">📖</a></td>
    <td align="center"><a href="http://www.spatialys.com/en/about/"><img src="https://avatars2.githubusercontent.com/u/1192433?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Even Rouault</b></sub></a><br /><a href="#question-rouault" title="Answering Questions">💬</a></td>
    <td align="center"><a href="https://github.com/cgohlke"><img src="https://avatars3.githubusercontent.com/u/483428?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Christoph Gohlke</b></sub></a><br /><a href="#platform-cgohlke" title="Packaging/porting to new platform">📦</a> <a href="#question-cgohlke" title="Answering Questions">💬</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Acgohlke" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=cgohlke" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/chrrrisw"><img src="https://avatars0.githubusercontent.com/u/5555320?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Chris Willoughby</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=chrrrisw" title="Code">💻</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/glostis"><img src="https://avatars0.githubusercontent.com/u/25295717?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Guillaume Lostis</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=glostis" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/edpop"><img src="https://avatars3.githubusercontent.com/u/13479292?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Eduard Popov</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=edpop" title="Documentation">📖</a></td>
    <td align="center"><a href="http://www.personal.psu.edu/jar339"><img src="https://avatars2.githubusercontent.com/u/7864460?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Joe Ranalli</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ajranalli" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jranalli" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jranalli" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/gberardinelli"><img src="https://avatars0.githubusercontent.com/u/13799588?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Greg Berardinelli</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Agberardinelli" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=gberardinelli" title="Code">💻</a> <a href="#ideas-gberardinelli" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/pyproj4/pyproj/commits?author=gberardinelli" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/mraspaud"><img src="https://avatars1.githubusercontent.com/u/167802?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Martin Raspaud</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Amraspaud" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=mraspaud" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=mraspaud" title="Tests">⚠️</a> <a href="#ideas-mraspaud" title="Ideas, Planning, & Feedback">🤔</a></td>
    <td align="center"><a href="https://sites.google.com/site/mwtoews/"><img src="https://avatars1.githubusercontent.com/u/895458?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Mike Taves</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=mwtoews" title="Tests">⚠️</a></td>
    <td align="center"><a href="http://davidhaberthür.ch"><img src="https://avatars2.githubusercontent.com/u/1651235?v=4?s=100" width="100px;" alt=""/><br /><sub><b>David Haberthür</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=habi" title="Documentation">📖</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/mmodenesi"><img src="https://avatars2.githubusercontent.com/u/5569789?v=4?s=100" width="100px;" alt=""/><br /><sub><b>mmodenesi</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ammodenesi" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=mmodenesi" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=mmodenesi" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://www.indigoag.com/"><img src="https://avatars0.githubusercontent.com/u/48448372?v=4?s=100" width="100px;" alt=""/><br /><sub><b>jacob-indigo</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ajacob-indigo" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jacob-indigo" title="Code">💻</a></td>
    <td align="center"><a href="https://rahulporuri.github.io"><img src="https://avatars0.githubusercontent.com/u/1926457?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Poruri Sai Rahul</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=rahulporuri" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://medium.com/@underchemist"><img src="https://avatars1.githubusercontent.com/u/5283998?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Yann-Sebastien Tremblay-Johnston</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=underchemist" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/odidev"><img src="https://avatars2.githubusercontent.com/u/40816837?v=4?s=100" width="100px;" alt=""/><br /><sub><b>odidev</b></sub></a><br /><a href="#platform-odidev" title="Packaging/porting to new platform">📦</a></td>
    <td align="center"><a href="https://github.com/idanmiara"><img src="https://avatars.githubusercontent.com/u/26349741?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Idan Miara</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=idanmiara" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=idanmiara" title="Documentation">📖</a> <a href="#example-idanmiara" title="Examples">💡</a> <a href="https://github.com/pyproj4/pyproj/commits?author=idanmiara" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/direvus"><img src="https://avatars.githubusercontent.com/u/312229?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Brendan Jurd</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=direvus" title="Documentation">📖</a> <a href="#design-direvus" title="Design">🎨</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://www.metoffice.gov.uk/"><img src="https://avatars.githubusercontent.com/u/2051656?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Bill Little</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=bjlittle" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/gerritholl"><img src="https://avatars.githubusercontent.com/u/500246?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Gerrit Holl</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=gerritholl" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/Kirill888"><img src="https://avatars.githubusercontent.com/u/1428024?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Kirill Kouzoubov</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=Kirill888" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/hemberger"><img src="https://avatars.githubusercontent.com/u/846186?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Dan Hemberger</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ahemberger" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=hemberger" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/martinfleis"><img src="https://avatars.githubusercontent.com/u/36797143?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Martin Fleischmann</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Amartinfleis" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=martinfleis" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=martinfleis" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/orontee"><img src="https://avatars.githubusercontent.com/u/2065954?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Matthias Meulien</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=orontee" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Aorontee" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://github.com/iboates"><img src="https://avatars.githubusercontent.com/u/13814358?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Isaac Boates</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=iboates" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Aiboates" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=iboates" title="Tests">⚠️</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/kdpenner"><img src="https://avatars.githubusercontent.com/u/9297904?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Kyle Penner</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=kdpenner" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Akdpenner" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=kdpenner" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/paulcochrane"><img src="https://avatars.githubusercontent.com/u/18310598?v=4?s=100" width="100px;" alt=""/><br /><sub><b>paulcochrane</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=paulcochrane" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=paulcochrane" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=paulcochrane" title="Tests">⚠️</a> <a href="https://github.com/pyproj4/pyproj/issues?q=author%3Apaulcochrane" title="Bug reports">🐛</a></td>
    <td align="center"><a href="https://github.com/vot4anto"><img src="https://avatars.githubusercontent.com/u/56338190?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Antonio Ettorre</b></sub></a><br /><a href="#platform-vot4anto" title="Packaging/porting to new platform">📦</a></td>
    <td align="center"><a href="https://github.com/DWesl"><img src="https://avatars.githubusercontent.com/u/22566757?v=4?s=100" width="100px;" alt=""/><br /><sub><b>DWesl</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=DWesl" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/molinav"><img src="https://avatars.githubusercontent.com/u/9979942?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Víctor Molina García</b></sub></a><br /><a href="#platform-molinav" title="Packaging/porting to new platform">📦</a></td>
    <td align="center"><a href="https://github.com/skogler"><img src="https://avatars.githubusercontent.com/u/1032405?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Samuel Kogler</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Askogler" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=skogler" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/shadchin"><img src="https://avatars.githubusercontent.com/u/61256?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Alexander Shadchin</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/issues?q=author%3Ashadchin" title="Bug reports">🐛</a> <a href="https://github.com/pyproj4/pyproj/commits?author=shadchin" title="Code">💻</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/greglucas"><img src="https://avatars.githubusercontent.com/u/12417828?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Greg Lucas</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=greglucas" title="Code">💻</a> <a href="#ideas-greglucas" title="Ideas, Planning, & Feedback">🤔</a> <a href="#maintenance-greglucas" title="Maintenance">🚧</a></td>
    <td align="center"><a href="https://github.com/dmahr1"><img src="https://avatars.githubusercontent.com/u/8354515?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Dan Mahr</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=dmahr1" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=dmahr1" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=dmahr1" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/rhugonnet"><img src="https://avatars.githubusercontent.com/u/28896516?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Romain Hugonnet</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=rhugonnet" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=rhugonnet" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=rhugonnet" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://javier.jimenezshaw.com/"><img src="https://avatars.githubusercontent.com/u/15678366?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Javier Jimenez Shaw</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=jjimenezshaw" title="Code">💻</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jjimenezshaw" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=jjimenezshaw" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/djm93dev"><img src="https://avatars.githubusercontent.com/u/101536185?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Daniel McDonald</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=djm93dev" title="Documentation">📖</a></td>
    <td align="center"><a href="https://cyschneck.com/"><img src="https://avatars.githubusercontent.com/u/22159116?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Cora Schneck</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=cyschneck" title="Documentation">📖</a> <a href="https://github.com/pyproj4/pyproj/commits?author=cyschneck" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/zanejgr"><img src="https://avatars.githubusercontent.com/u/14795919?v=4?s=100" width="100px;" alt=""/><br /><sub><b>zanejgr</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=zanejgr" title="Documentation">📖</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/kloczek"><img src="https://avatars.githubusercontent.com/u/31284574?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Tomasz Kłoczko</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=kloczek" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/tqa236"><img src="https://avatars.githubusercontent.com/u/25203655?v=4?s=100" width="100px;" alt=""/><br /><sub><b>Trinh Quoc Anh</b></sub></a><br /><a href="https://github.com/pyproj4/pyproj/commits?author=tqa236" title="Tests">⚠️</a></td>
    <td align="center"><a href="https://github.com/necabo"><img src="https://avatars.githubusercontent.com/u/23185845?v=4?s=100" width="100px;" alt=""/><br /><sub><b>necabo</b></sub></a><br /><a href="#platform-necabo" title="Packaging/porting to new platform">📦</a></td>
  </tr>
</table>

<!-- markdownlint-restore -->
<!-- prettier-ignore-end -->

<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification. Contributions of any kind welcome!
