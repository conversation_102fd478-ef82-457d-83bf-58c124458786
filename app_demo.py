import streamlit as st
import folium
from streamlit_folium import st_folium
import pandas as pd
import random
from datetime import datetime

# Demo mode - no APIs needed
st.set_page_config(
    page_title="Solar Neighborhood Analyzer - Demo",
    page_icon="☀️",
    layout="wide"
)

st.title("☀️ Solar Neighborhood Analyzer - Demo Mode")
st.info("🎯 Running in demo mode with simulated data")

# Sidebar
with st.sidebar:
    address = st.text_input("Enter Address", value="1 Rocket Road, Hawthorne, CA")
    if st.button("🔍 Analyze Neighborhood"):
        
        # Generate fake but realistic data
        with st.spinner("Analyzing..."):
            # Create sample data
            homes_data = []
            
            # Center home
            homes_data.append({
                'id': 0,
                'label': 'Your Solar Home',
                'lat': 33.9207 + random.uniform(-0.001, 0.001),
                'lng': -118.3280 + random.uniform(-0.001, 0.001),
                'type': 'center',
                'potential_category': 'Existing Solar',
                'yearly_energy_kwh': 0,
                'carbon_offset_kg': 0,
                'roof_area_sqm': 150
            })
            
            # Neighboring homes
            categories = ['Excellent', 'Excellent', 'Good', 'Good', 'Moderate', 'Low']
            for i in range(6):
                category = categories[i]
                if category == 'Excellent':
                    energy = random.randint(6000, 8000)
                elif category == 'Good':
                    energy = random.randint(4000, 6000)
                elif category == 'Moderate':
                    energy = random.randint(2500, 4000)
                else:
                    energy = random.randint(1000, 2500)
                
                homes_data.append({
                    'id': i + 1,
                    'label': f'Home {i + 1}',
                    'lat': 33.9207 + random.uniform(-0.002, 0.002),
                    'lng': -118.3280 + random.uniform(-0.002, 0.002),
                    'type': 'neighbor',
                    'potential_category': category,
                    'yearly_energy_kwh': energy,
                    'carbon_offset_kg': energy * 0.5,
                    'roof_area_sqm': random.randint(80, 200)
                })
            
            df = pd.DataFrame(homes_data)
            st.session_state.results = df

# Display results
if 'results' in st.session_state:
    df = st.session_state.results
    
    # Create map
    m = folium.Map(location=[33.9207, -118.3280], zoom_start=16)
    
    # Add markers
    for _, home in df.iterrows():
        color = 'blue' if home['type'] == 'center' else {
            'Excellent': 'green',
            'Good': 'lightgreen', 
            'Moderate': 'orange',
            'Low': 'red'
        }.get(home['potential_category'], 'gray')
        
        folium.Marker(
            [home['lat'], home['lng']],
            popup=f"{home['label']}<br>{home['potential_category']}<br>{home['yearly_energy_kwh']} kWh/yr",
            icon=folium.Icon(color=color)
        ).add_to(m)
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("🗺️ Neighborhood Map")
        st_folium(m, height=500)
    
    with col2:
        st.subheader("📊 Summary")
        neighbors = df[df['type'] != 'center']
        
        st.metric("High Potential Homes", 
                  len(neighbors[neighbors['potential_category'].isin(['Excellent', 'Good'])]))
        st.metric("Total Energy Potential", 
                  f"{neighbors['yearly_energy_kwh'].sum():,} kWh/yr")
        st.metric("CO₂ Offset Potential", 
                  f"{neighbors['carbon_offset_kg'].sum()/1000:.1f} tons/yr")
        
        st.subheader("🏆 Top Candidates")
        top = neighbors.nlargest(3, 'yearly_energy_kwh')[['label', 'yearly_energy_kwh']]
        st.dataframe(top)