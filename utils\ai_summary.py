import google.generativeai as genai
import os
from dotenv import load_dotenv

load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv('GEMINI_API_KEY'))

def generate_neighborhood_summary(solar_data_df, address):
    """Generate AI summary using Gemini"""
    
    # Prepare data summary
    total_homes = len(solar_data_df) - 1  # Exclude center home
    excellent_homes = len(solar_data_df[solar_data_df['potential_category'] == 'Excellent'])
    good_homes = len(solar_data_df[solar_data_df['potential_category'] == 'Good'])
    moderate_homes = len(solar_data_df[solar_data_df['potential_category'] == 'Moderate'])
    
    # Calculate totals (excluding center home)
    neighbor_data = solar_data_df[solar_data_df['type'] != 'center']
    total_potential_energy = neighbor_data['yearly_energy_kwh'].sum()
    total_carbon_offset = neighbor_data['carbon_offset_kg'].sum() / 1000  # tons
    avg_energy_per_home = neighbor_data['yearly_energy_kwh'].mean()
    
    # Create prompt for Gemini
    prompt = f"""
    As a solar energy consultant, create a professional summary for a neighborhood solar analysis.
    
    Location: {address}
    Analysis Results:
    - Total neighboring homes analyzed: {total_homes}
    - Excellent solar potential: {excellent_homes} homes
    - Good solar potential: {good_homes} homes
    - Moderate solar potential: {moderate_homes} homes
    - Total potential energy generation: {total_potential_energy:,.0f} kWh/year
    - Total carbon offset potential: {total_carbon_offset:.1f} tons CO2/year
    - Average energy per home: {avg_energy_per_home:,.0f} kWh/year
    
    Write a 3-4 sentence executive summary that:
    1. Highlights the best opportunities
    2. Quantifies the environmental impact
    3. Provides a clear recommendation
    
    Use encouraging but professional tone. Include relevant emojis.
    """
    
    try:
        model = genai.GenerativeModel('gemini-pro')
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        # Fallback summary if Gemini fails
        return f"""
        ☀️ **Neighborhood Solar Analysis Complete**
        
        This analysis identified {excellent_homes + good_homes} high-potential candidates 
        for solar installation near {address}. Installing solar panels on these homes 
        could generate {total_potential_energy:,.0f} kWh annually and offset {total_carbon_offset:.1f} 
        tons of CO2 per year. 
        
        💡 We recommend prioritizing the {excellent_homes} excellent candidates for immediate 
        solar adoption to maximize environmental and economic benefits.
        """