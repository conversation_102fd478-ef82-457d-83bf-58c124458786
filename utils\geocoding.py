import requests
import os
import random
import math
from dotenv import load_dotenv

load_dotenv()

def geocode_address(address):
    """Convert address to latitude and longitude"""
    api_key = os.getenv('GOOGLE_API_KEY')
    
    if not api_key:
        return {
            'success': False,
            'error': 'Google API key not found in .env file'
        }
    
    base_url = "https://maps.googleapis.com/maps/api/geocode/json"
    
    params = {
        'address': address,
        'key': api_key
    }
    
    try:
        response = requests.get(base_url, params=params)
        data = response.json()
        
        # Debug print
        if data['status'] != 'OK':
            print(f"Google API Response: {data}")
        
        if data['status'] == 'OK':
            location = data['results'][0]['geometry']['location']
            formatted_address = data['results'][0]['formatted_address']
            return {
                'lat': location['lat'],
                'lng': location['lng'],
                'formatted_address': formatted_address,
                'success': True
            }
        elif data['status'] == 'REQUEST_DENIED':
            return {
                'success': False,
                'error': f"API request denied. Check if Geocoding API is enabled for your key"
            }
        elif data['status'] == 'INVALID_REQUEST':
            return {
                'success': False,
                'error': f"Invalid request. Check API key format"
            }
        else:
            return {
                'success': False,
                'error': f"Geocoding failed: {data.get('status', 'Unknown error')} - {data.get('error_message', '')}"
            }
    except Exception as e:
        return {
            'success': False,
            'error': f"Request failed: {str(e)}"
        }

def find_nearby_addresses(lat, lng, radius_meters=200, max_results=10):
    """Find actual nearby addresses using Google Places API"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return {
            'success': False,
            'error': 'Google API key not found in .env file'
        }

    # Use Places API to find nearby establishments/addresses
    base_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"

    params = {
        'location': f"{lat},{lng}",
        'radius': radius_meters,
        'type': 'establishment',  # This will find businesses, which often have addresses
        'key': api_key
    }

    try:
        response = requests.get(base_url, params=params)
        data = response.json()

        if data['status'] == 'OK':
            addresses = []
            for place in data.get('results', [])[:max_results]:
                place_lat = place['geometry']['location']['lat']
                place_lng = place['geometry']['location']['lng']

                # Calculate distance from center
                distance = calculate_distance(lat, lng, place_lat, place_lng)

                addresses.append({
                    'lat': place_lat,
                    'lng': place_lng,
                    'name': place.get('name', 'Unknown'),
                    'address': place.get('vicinity', ''),
                    'distance_meters': distance,
                    'place_id': place.get('place_id', ''),
                    'types': place.get('types', [])
                })

            # Sort by distance
            addresses.sort(key=lambda x: x['distance_meters'])

            return {
                'success': True,
                'addresses': addresses
            }
        else:
            return {
                'success': False,
                'error': f"Places API failed: {data.get('status', 'Unknown error')}"
            }

    except Exception as e:
        return {
            'success': False,
            'error': f"Request failed: {str(e)}"
        }

def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points in meters using Haversine formula"""
    R = 6371000  # Earth's radius in meters

    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lng = math.radians(lng2 - lng1)

    a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lng/2) * math.sin(delta_lng/2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))

    return R * c

def generate_residential_addresses_around_point(lat, lng, radius_meters=200, num_addresses=8):
    """Generate realistic residential addresses around a point by finding nearby places and creating variations"""

    # First try to find actual nearby places
    nearby_result = find_nearby_addresses(lat, lng, radius_meters, max_results=15)

    addresses = []

    if nearby_result['success'] and nearby_result['addresses']:
        # Use actual nearby places as a base
        actual_places = nearby_result['addresses']

        # Create residential variations around these actual places
        for i, place in enumerate(actual_places[:num_addresses]):
            # Create slight variations to simulate nearby houses
            lat_offset = random.uniform(-0.0005, 0.0005)  # ~50m variation
            lng_offset = random.uniform(-0.0005, 0.0005)

            # Generate a residential address based on the nearby place
            base_address = place['address'] if place['address'] else place['name']

            # Create residential address variations
            house_numbers = [str(random.randint(100, 999)) for _ in range(3)]
            street_variations = [
                f"{house_numbers[0]} {extract_street_name(base_address)}",
                f"{house_numbers[1]} {extract_street_name(base_address)} St",
                f"{house_numbers[2]} {extract_street_name(base_address)} Ave"
            ]

            residential_address = random.choice(street_variations)

            addresses.append({
                'lat': place['lat'] + lat_offset,
                'lng': place['lng'] + lng_offset,
                'address': residential_address,
                'distance_meters': place['distance_meters'],
                'type': 'residential_variation'
            })

    # If we don't have enough addresses, fill with generated ones
    while len(addresses) < num_addresses:
        # Generate addresses in a circular pattern
        angle = (len(addresses) * 360 / num_addresses) + random.uniform(-30, 30)
        distance = random.uniform(radius_meters * 0.3, radius_meters * 0.9)

        # Convert to lat/lng offset
        lat_offset = (distance * math.cos(math.radians(angle))) / 111000
        lng_offset = (distance * math.sin(math.radians(angle))) / (111000 * math.cos(math.radians(lat)))

        new_lat = lat + lat_offset
        new_lng = lng + lng_offset

        # Generate a realistic address
        street_names = ["Oak St", "Main St", "Elm Ave", "Park Dr", "Cedar Ln", "Pine St", "Maple Ave"]
        house_number = random.randint(100, 999)
        street = random.choice(street_names)

        addresses.append({
            'lat': new_lat,
            'lng': new_lng,
            'address': f"{house_number} {street}",
            'distance_meters': distance,
            'type': 'generated'
        })

    return addresses[:num_addresses]

def extract_street_name(address):
    """Extract a reasonable street name from an address or place name"""
    if not address:
        return "Main"

    # Remove common business words and numbers
    words = address.replace(',', ' ').split()
    filtered_words = []

    for word in words:
        # Skip numbers, common business words
        if (not word.isdigit() and
            word.lower() not in ['the', 'and', 'of', 'at', 'in', 'on', 'store', 'shop', 'center', 'plaza']):
            filtered_words.append(word)

    if filtered_words:
        return filtered_words[0]
    else:
        return "Main"