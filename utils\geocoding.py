import requests
import os
from dotenv import load_dotenv

load_dotenv()

def geocode_address(address):
    """Convert address to latitude and longitude"""
    api_key = os.getenv('GOOGLE_API_KEY')
    
    if not api_key:
        return {
            'success': False,
            'error': 'Google API key not found in .env file'
        }
    
    base_url = "https://maps.googleapis.com/maps/api/geocode/json"
    
    params = {
        'address': address,
        'key': api_key
    }
    
    try:
        response = requests.get(base_url, params=params)
        data = response.json()
        
        # Debug print
        if data['status'] != 'OK':
            print(f"Google API Response: {data}")
        
        if data['status'] == 'OK':
            location = data['results'][0]['geometry']['location']
            formatted_address = data['results'][0]['formatted_address']
            return {
                'lat': location['lat'],
                'lng': location['lng'],
                'formatted_address': formatted_address,
                'success': True
            }
        elif data['status'] == 'REQUEST_DENIED':
            return {
                'success': False,
                'error': f"API request denied. Check if Geocoding API is enabled for your key"
            }
        elif data['status'] == 'INVALID_REQUEST':
            return {
                'success': False,
                'error': f"Invalid request. Check API key format"
            }
        else:
            return {
                'success': False,
                'error': f"Geocoding failed: {data.get('status', 'Unknown error')} - {data.get('error_message', '')}"
            }
    except Exception as e:
        return {
            'success': False,
            'error': f"Request failed: {str(e)}"
        }