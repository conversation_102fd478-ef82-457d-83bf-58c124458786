import math

def generate_nearby_homes(center_lat, center_lng, num_homes=8, radius_meters=100):
    """Generate coordinates for nearby homes in a circular pattern"""
    homes = []
    
    # Add center home
    homes.append({
        'id': 0,
        'lat': center_lat,
        'lng': center_lng,
        'type': 'center',
        'label': 'Reference Solar Home',
        'address_suffix': '(Your Input)'
    })
    
    # Calculate offset in degrees (rough approximation)
    # 1 degree latitude ≈ 111 km
    # 1 degree longitude ≈ 111 km * cos(latitude)
    lat_offset = radius_meters / 111000
    lng_offset = radius_meters / (111000 * math.cos(math.radians(center_lat)))
    
    # Generate surrounding homes
    angle_step = 360 / num_homes
    
    for i in range(num_homes):
        angle = math.radians(i * angle_step)
        
        # Calculate position
        lat = center_lat + (lat_offset * math.sin(angle))
        lng = center_lng + (lng_offset * math.cos(angle))
        
        # Determine direction label
        if 337.5 <= (i * angle_step) or (i * angle_step) < 22.5:
            direction = "North"
        elif 22.5 <= (i * angle_step) < 67.5:
            direction = "Northeast"
        elif 67.5 <= (i * angle_step) < 112.5:
            direction = "East"
        elif 112.5 <= (i * angle_step) < 157.5:
            direction = "Southeast"
        elif 157.5 <= (i * angle_step) < 202.5:
            direction = "South"
        elif 202.5 <= (i * angle_step) < 247.5:
            direction = "Southwest"
        elif 247.5 <= (i * angle_step) < 292.5:
            direction = "West"
        else:
            direction = "Northwest"
        
        homes.append({
            'id': i + 1,
            'lat': lat,
            'lng': lng,
            'type': 'neighbor',
            'label': f'Home {i + 1}',
            'address_suffix': f'({direction})'
        })
    
    return homes