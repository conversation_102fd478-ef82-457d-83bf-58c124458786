[general]
; Lines starting by ; are commented lines.
;

; Network capabilities disabled by default.
; Can be overridden with the PROJ_NETWORK=ON environment variable.
; Cf https://proj.org/en/latest/usage/network.html
; Valid values = on, off
network = off

; Endpoint of the Content Delivery Network where remote resources might
; be accessed. Only used if network access is allowed (cf above "network"
; option)
; Can be overridden with the PROJ_NETWORK_ENDPOINT environment variable.
cdn_endpoint = https://cdn.proj.org

; Whether to enable a cache of remote resources that are accessed, on the
; local file system
; Valid values = on, off
cache_enabled = on

; Size of the cache in megabytes
cache_size_MB = 300

; Time-to-live delay in seconds before already accessed remote resources are
; accessed again to check if they have been updated.
cache_ttl_sec = 86400

; Can be set to on so that by default the lack of a known resource files needed
; for the best transformation PROJ would normally use causes an error, or off
; to accept missing resource files without errors or warnings.
; This default value itself is overridden by the PROJ_ONLY_BEST_DEFAULT environment
; variable if set, and then by the ONLY_BEST setting that can be
; passed to the proj_create_crs_to_crs() method, or with the --only-best
; option of the cs2cs program.
; (added in PROJ 9.2)
; Valid values = on, off
only_best_default = off

; Filename of the Certificate Authority (CA) bundle.
; Can be overridden with the PROJ_CURL_CA_BUNDLE / CURL_CA_BUNDLE environment variable.
; (added in PROJ 9.0)
; ca_bundle_path = /path/to/cabundle.pem

; When this is set to on, the operating systems native CA store will be used for certificate verification
; If you set this option to on and also set ca_bundle_path then during verification those certificates are
; searched in addition to the native CA store.
; (added in PROJ 9.6)
; Valid values = on, off
;native_ca = on


; Transverse Mercator (and UTM)  default algorithm: auto, evenden_snyder or poder_engsager
; * evenden_snyder is the fastest, but less accurate far from central meridian
; * poder_engsager is slower, but more accurate far from central meridian
; * default will auto-select between the two above depending on the coordinate
;   to transform and will use evenden_snyder if the error in doing so is below
;   0.1 mm (for an ellipsoid of the size of Earth)
tmerc_default_algo = poder_engsager
