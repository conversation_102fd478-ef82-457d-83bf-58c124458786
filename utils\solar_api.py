import requests
import os
import random
from dotenv import load_dotenv

load_dotenv()

def get_solar_potential(lat, lng):
    """Get solar potential data from Google Solar API with enhanced building detection"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return generate_simulated_data()

    # Solar API endpoint
    url = "https://solar.googleapis.com/v1/buildingInsights:findClosest"

    params = {
        'location.latitude': lat,
        'location.longitude': lng,
        'requiredQuality': 'MEDIUM',  # Changed from HIGH to get more results
        'key': api_key
    }

    try:
        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()

            # Check if we actually found a building
            if 'center' not in data or 'solarPotential' not in data:
                return generate_simulated_data()

            # Extract building location (actual building coordinates)
            center = data.get('center', {})
            building_lat = center.get('latitude', lat)
            building_lng = center.get('longitude', lng)

            # Verify this is actually a building (not just a random point)
            # Check if the returned coordinates are significantly different from input
            lat_diff = abs(building_lat - lat)
            lng_diff = abs(building_lng - lng)

            # If the API returned coordinates very far from our search point, it might not be a real building
            if lat_diff > 0.001 or lng_diff > 0.001:  # More than ~100m difference
                return generate_simulated_data()

            # Extract solar potential data
            solar_potential = data.get('solarPotential', {})

            # Get yearly sunshine hours
            max_sunshine_hours = solar_potential.get('maxSunshineHoursPerYear', 0)

            # Get roof segment stats
            roof_segments = solar_potential.get('roofSegmentStats', [])
            total_roof_area = sum(segment.get('stats', {}).get('areaMeters2', 0)
                                for segment in roof_segments)

            # Only proceed if we have meaningful roof area data
            if total_roof_area < 10:  # Less than 10 sqm is probably not a real building
                return generate_simulated_data()

            # Check for existing solar installations
            solar_panels = solar_potential.get('solarPanels', [])
            has_existing_solar = len(solar_panels) > 0

            # Get solar panel configurations if available
            solar_panel_configs = solar_potential.get('solarPanelConfigs', [])

            # Calculate potential metrics
            if solar_panel_configs:
                # Use the best configuration
                best_config = max(solar_panel_configs,
                                key=lambda x: x.get('yearlyEnergyDcKwh', 0))
                yearly_energy_kwh = best_config.get('yearlyEnergyDcKwh', 0)
                panel_capacity_kw = best_config.get('panelsCount', 0) * 0.4  # Assume 400W panels
            else:
                # Fallback calculation
                panel_capacity_kw = min(total_roof_area / 10, 15)  # Increased cap to 15kW
                yearly_energy_kwh = panel_capacity_kw * 1500

            # Carbon offset: ~0.5 kg CO2 per kWh
            carbon_offset_kg = yearly_energy_kwh * 0.5

            return {
                'success': True,
                'lat': building_lat,
                'lng': building_lng,
                'yearly_energy_kwh': yearly_energy_kwh,
                'roof_area_sqm': total_roof_area,
                'carbon_offset_kg': carbon_offset_kg,
                'panel_capacity_kw': panel_capacity_kw,
                'sunshine_hours': max_sunshine_hours,
                'has_existing_solar': has_existing_solar,
                'existing_panels_count': len(solar_panels),
                'data_quality': 'actual'
            }
        else:
            # Return simulated data if API fails
            return generate_simulated_data()

    except Exception as e:
        # Return simulated data on error
        return generate_simulated_data()

def generate_simulated_data():
    """Generate realistic simulated solar data for demo"""
    roof_area = random.uniform(80, 200)
    panel_capacity = min(roof_area / 10, 10)
    yearly_energy = panel_capacity * random.uniform(1300, 1700)
    
    return {
        'success': True,
        'yearly_energy_kwh': yearly_energy,
        'roof_area_sqm': roof_area,
        'carbon_offset_kg': yearly_energy * 0.5,
        'panel_capacity_kw': panel_capacity,
        'sunshine_hours': random.uniform(1800, 2200),
        'data_quality': 'simulated'
    }

def categorize_potential(yearly_energy_kwh):
    """Categorize solar potential based on yearly energy generation"""
    if yearly_energy_kwh >= 6000:
        return {
            'category': 'Excellent',
            'color': '#00ff00',
            'icon': '🟢',
            'priority': 1
        }
    elif yearly_energy_kwh >= 4000:
        return {
            'category': 'Good',
            'color': '#90EE90',
            'icon': '🟢',
            'priority': 2
        }
    elif yearly_energy_kwh >= 2500:
        return {
            'category': 'Moderate',
            'color': '#FFA500',
            'icon': '🟡',
            'priority': 3
        }
    else:
        return {
            'category': 'Low',
            'color': '#FF6B6B',
            'icon': '🔴',
            'priority': 4
        }

def find_buildings_in_area(center_lat, center_lng, radius_meters=200, max_buildings=15):
    """Find actual buildings with comprehensive solar data using Google Solar API"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return {
            'success': False,
            'error': 'Google API key not found'
        }

    buildings = []

    # Use a comprehensive search strategy
    # First, try to find buildings using Places API for better coverage
    places_buildings = find_buildings_using_places_api(center_lat, center_lng, radius_meters, api_key)

    # Then supplement with grid search for any missed buildings
    grid_buildings = find_buildings_using_grid_search(center_lat, center_lng, radius_meters, api_key)

    # Combine and deduplicate
    all_buildings = places_buildings + grid_buildings

    # Remove duplicates and get solar data for each
    for building in all_buildings:
        if len(buildings) >= max_buildings:
            break

        # Check for duplicates
        is_duplicate = False
        for existing in buildings:
            lat_diff = abs(existing['lat'] - building['lat'])
            lng_diff = abs(existing['lng'] - building['lng'])
            if lat_diff < 0.0001 and lng_diff < 0.0001:  # ~10m threshold
                is_duplicate = True
                break

        if not is_duplicate:
            # Get comprehensive solar data for this building
            solar_data = get_comprehensive_solar_data(building['lat'], building['lng'], api_key)
            if solar_data['success']:
                buildings.append(solar_data)

    return {
        'success': True,
        'buildings': buildings,
        'total_found': len(buildings)
    }

def find_buildings_using_places_api(center_lat, center_lng, radius_meters, api_key):
    """Find buildings using Google Places API for better accuracy"""
    buildings = []

    try:
        # Search for residential and commercial buildings
        place_types = ['establishment', 'point_of_interest']

        for place_type in place_types:
            url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
            params = {
                'location': f"{center_lat},{center_lng}",
                'radius': radius_meters,
                'type': place_type,
                'key': api_key
            }

            response = requests.get(url, params=params)
            data = response.json()

            if data['status'] == 'OK':
                for place in data.get('results', []):
                    location = place['geometry']['location']
                    buildings.append({
                        'lat': location['lat'],
                        'lng': location['lng'],
                        'name': place.get('name', 'Unknown Building'),
                        'types': place.get('types', [])
                    })

    except Exception as e:
        print(f"Places API error: {e}")

    return buildings

def find_buildings_using_grid_search(center_lat, center_lng, radius_meters, api_key):
    """Supplement with grid search to find any missed buildings"""
    buildings = []

    # Create a systematic grid
    lat_offset = radius_meters / 111000
    lng_offset = radius_meters / (111000 * max(abs(center_lat / 90), 0.1))

    # Use a 9x9 grid for comprehensive coverage
    grid_size = 9
    step_size = 2.0 / (grid_size - 1)

    for i in range(grid_size):
        for j in range(grid_size):
            norm_i = -1 + i * step_size
            norm_j = -1 + j * step_size

            search_lat = center_lat + (norm_i * lat_offset)
            search_lng = center_lng + (norm_j * lng_offset)

            # Check if within radius
            distance = ((search_lat - center_lat) * 111000) ** 2 + \
                      ((search_lng - center_lng) * 111000 * max(abs(center_lat / 90), 0.1)) ** 2
            distance = distance ** 0.5

            if distance <= radius_meters:
                buildings.append({
                    'lat': search_lat,
                    'lng': search_lng,
                    'name': f'Grid Point {i},{j}',
                    'types': ['grid_search']
                })

    return buildings

def get_comprehensive_solar_data(lat, lng, api_key):
    """Get comprehensive solar data including all building types and conditions"""

    # First, try to get building insights
    building_data = get_detailed_building_insights(lat, lng, api_key)

    if building_data['success']:
        return building_data
    else:
        # If no real data, create realistic simulated data based on location
        return generate_location_based_simulated_data(lat, lng)

def get_detailed_building_insights(lat, lng, api_key):
    """Get detailed building insights from Google Solar API"""

    url = "https://solar.googleapis.com/v1/buildingInsights:findClosest"

    params = {
        'location.latitude': lat,
        'location.longitude': lng,
        'requiredQuality': 'LOW',  # Use LOW to get more results
        'key': api_key
    }

    try:
        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()

            # Check if we have valid building data
            if 'center' not in data:
                return {'success': False}

            # Extract building location
            center = data.get('center', {})
            building_lat = center.get('latitude', lat)
            building_lng = center.get('longitude', lng)

            # Extract solar potential data
            solar_potential = data.get('solarPotential', {})

            # Get roof segment stats
            roof_segments = solar_potential.get('roofSegmentStats', [])
            total_roof_area = sum(segment.get('stats', {}).get('areaMeters2', 0)
                                for segment in roof_segments)

            # Check for existing solar installations
            solar_panels = solar_potential.get('solarPanels', [])
            has_existing_solar = len(solar_panels) > 0

            # Get solar panel configurations
            solar_panel_configs = solar_potential.get('solarPanelConfigs', [])

            # Calculate metrics based on real data
            if solar_panel_configs and len(solar_panel_configs) > 0:
                # Use the configuration with highest energy output
                best_config = max(solar_panel_configs,
                                key=lambda x: x.get('yearlyEnergyDcKwh', 0))
                yearly_energy_kwh = best_config.get('yearlyEnergyDcKwh', 0)
                panel_capacity_kw = best_config.get('panelsCount', 0) * 0.4
            else:
                # Calculate based on roof area and sunshine
                max_sunshine_hours = solar_potential.get('maxSunshineHoursPerYear', 1800)
                panel_capacity_kw = min(total_roof_area / 10, 20)  # 10 sqm per kW, max 20kW
                yearly_energy_kwh = panel_capacity_kw * max_sunshine_hours * 0.8  # 80% efficiency

            # Carbon offset calculation
            carbon_offset_kg = yearly_energy_kwh * 0.5

            # Get building address
            address = get_building_address(building_lat, building_lng)

            return {
                'success': True,
                'lat': building_lat,
                'lng': building_lng,
                'yearly_energy_kwh': yearly_energy_kwh,
                'roof_area_sqm': total_roof_area,
                'carbon_offset_kg': carbon_offset_kg,
                'panel_capacity_kw': panel_capacity_kw,
                'sunshine_hours': solar_potential.get('maxSunshineHoursPerYear', 1800),
                'has_existing_solar': has_existing_solar,
                'existing_panels_count': len(solar_panels),
                'data_source': 'Google Solar API',
                'address': address,
                'roof_segments_count': len(roof_segments)
            }
        else:
            return {'success': False}

    except Exception as e:
        return {'success': False}

def generate_location_based_simulated_data(lat, lng):
    """Generate realistic simulated data based on location characteristics"""

    # Create varied solar potential based on realistic factors
    roof_orientations = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest']
    roof_orientation = random.choice(roof_orientations)

    # Orientation multipliers (South is best, North is worst)
    orientation_multipliers = {
        'South': 1.0,
        'Southeast': 0.95,
        'Southwest': 0.95,
        'East': 0.85,
        'West': 0.85,
        'Northeast': 0.75,
        'Northwest': 0.75,
        'North': 0.6
    }

    # Random factors for realistic variation
    roof_area = random.uniform(80, 350)  # Wide range of roof sizes
    shading_factor = random.uniform(0.5, 1.0)  # Trees, buildings, etc.
    roof_age_factor = random.uniform(0.8, 1.0)  # Older roofs may be less suitable

    # Base potential calculation
    base_potential = 5000  # Base kWh/year
    orientation_factor = orientation_multipliers[roof_orientation]

    yearly_energy_kwh = (base_potential * orientation_factor *
                        shading_factor * roof_age_factor *
                        (roof_area / 150))  # Normalize to average roof

    # Randomly assign existing solar (about 10% of buildings)
    has_existing_solar = random.random() < 0.1

    if has_existing_solar:
        # If has existing solar, adjust the energy output
        yearly_energy_kwh = yearly_energy_kwh * random.uniform(0.7, 1.2)

    panel_capacity_kw = yearly_energy_kwh / 1500  # Rough conversion
    carbon_offset_kg = yearly_energy_kwh * 0.5

    # Get address for this location
    address = get_building_address(lat, lng)

    return {
        'success': True,
        'lat': lat,
        'lng': lng,
        'yearly_energy_kwh': yearly_energy_kwh,
        'roof_area_sqm': roof_area,
        'carbon_offset_kg': carbon_offset_kg,
        'panel_capacity_kw': panel_capacity_kw,
        'sunshine_hours': random.uniform(1600, 2200),
        'has_existing_solar': has_existing_solar,
        'existing_panels_count': random.randint(10, 30) if has_existing_solar else 0,
        'data_source': 'Simulated',
        'address': address,
        'roof_orientation': roof_orientation,
        'shading_factor': shading_factor
    }

def get_building_address(lat, lng):
    """Get a proper address for a building using reverse geocoding"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return f"Building at {lat:.5f}, {lng:.5f}"

    try:
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'latlng': f"{lat},{lng}",
            'key': api_key,
            'result_type': 'street_address|premise'
        }

        response = requests.get(url, params=params)
        data = response.json()

        if data['status'] == 'OK' and data['results']:
            return data['results'][0]['formatted_address']
        else:
            return f"Building at {lat:.5f}, {lng:.5f}"

    except Exception:
        return f"Building at {lat:.5f}, {lng:.5f}"