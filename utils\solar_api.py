import requests
import os
import random
from dotenv import load_dotenv

load_dotenv()

def get_solar_potential(lat, lng):
    """Get solar potential data from Google Solar API"""
    api_key = os.getenv('GOOGLE_API_KEY')
    
    # Solar API endpoint
    url = "https://solar.googleapis.com/v1/buildingInsights:findClosest"
    
    params = {
        'location.latitude': lat,
        'location.longitude': lng,
        'requiredQuality': 'HIGH',
        'key': api_key
    }
    
    try:
        response = requests.get(url, params=params)
        
        if response.status_code == 200:
            data = response.json()
            
            # Extract solar potential data
            solar_potential = data.get('solarPotential', {})
            
            # Get yearly sunshine hours
            max_sunshine_hours = solar_potential.get('maxSunshineHoursPerYear', 0)
            
            # Get roof segment stats
            roof_segments = solar_potential.get('roofSegmentStats', [])
            total_roof_area = sum(segment.get('stats', {}).get('areaMeters2', 0) 
                                for segment in roof_segments)
            
            # Calculate potential metrics
            # Rough estimates: 1 kW system produces ~1,500 kWh/year
            # 10 sqm ≈ 1 kW capacity
            panel_capacity_kw = min(total_roof_area / 10, 10)  # Cap at 10kW
            yearly_energy_kwh = panel_capacity_kw * 1500
            
            # Carbon offset: ~0.5 kg CO2 per kWh
            carbon_offset_kg = yearly_energy_kwh * 0.5
            
            return {
                'success': True,
                'yearly_energy_kwh': yearly_energy_kwh,
                'roof_area_sqm': total_roof_area,
                'carbon_offset_kg': carbon_offset_kg,
                'panel_capacity_kw': panel_capacity_kw,
                'sunshine_hours': max_sunshine_hours,
                'data_quality': 'actual'
            }
        else:
            # Return simulated data if API fails
            return generate_simulated_data()
            
    except Exception as e:
        # Return simulated data on error
        return generate_simulated_data()

def generate_simulated_data():
    """Generate realistic simulated solar data for demo"""
    roof_area = random.uniform(80, 200)
    panel_capacity = min(roof_area / 10, 10)
    yearly_energy = panel_capacity * random.uniform(1300, 1700)
    
    return {
        'success': True,
        'yearly_energy_kwh': yearly_energy,
        'roof_area_sqm': roof_area,
        'carbon_offset_kg': yearly_energy * 0.5,
        'panel_capacity_kw': panel_capacity,
        'sunshine_hours': random.uniform(1800, 2200),
        'data_quality': 'simulated'
    }

def categorize_potential(yearly_energy_kwh):
    """Categorize solar potential based on yearly energy generation"""
    if yearly_energy_kwh >= 6000:
        return {
            'category': 'Excellent',
            'color': '#00ff00',
            'icon': '🟢',
            'priority': 1
        }
    elif yearly_energy_kwh >= 4000:
        return {
            'category': 'Good',
            'color': '#90EE90',
            'icon': '🟢',
            'priority': 2
        }
    elif yearly_energy_kwh >= 2500:
        return {
            'category': 'Moderate',
            'color': '#FFA500',
            'icon': '🟡',
            'priority': 3
        }
    else:
        return {
            'category': 'Low',
            'color': '#FF6B6B',
            'icon': '🔴',
            'priority': 4
        }