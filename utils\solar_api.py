import requests
import os
import random
from dotenv import load_dotenv

load_dotenv()

def get_solar_potential(lat, lng):
    """Get solar potential data from Google Solar API with enhanced building detection"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return generate_simulated_data()

    # Solar API endpoint
    url = "https://solar.googleapis.com/v1/buildingInsights:findClosest"

    params = {
        'location.latitude': lat,
        'location.longitude': lng,
        'requiredQuality': 'MEDIUM',  # Changed from HIGH to get more results
        'key': api_key
    }

    try:
        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()

            # Check if we actually found a building
            if 'center' not in data or 'solarPotential' not in data:
                return generate_simulated_data()

            # Extract building location (actual building coordinates)
            center = data.get('center', {})
            building_lat = center.get('latitude', lat)
            building_lng = center.get('longitude', lng)

            # Verify this is actually a building (not just a random point)
            # Check if the returned coordinates are significantly different from input
            lat_diff = abs(building_lat - lat)
            lng_diff = abs(building_lng - lng)

            # If the API returned coordinates very far from our search point, it might not be a real building
            if lat_diff > 0.001 or lng_diff > 0.001:  # More than ~100m difference
                return generate_simulated_data()

            # Extract solar potential data
            solar_potential = data.get('solarPotential', {})

            # Get yearly sunshine hours
            max_sunshine_hours = solar_potential.get('maxSunshineHoursPerYear', 0)

            # Get roof segment stats
            roof_segments = solar_potential.get('roofSegmentStats', [])
            total_roof_area = sum(segment.get('stats', {}).get('areaMeters2', 0)
                                for segment in roof_segments)

            # Only proceed if we have meaningful roof area data
            if total_roof_area < 10:  # Less than 10 sqm is probably not a real building
                return generate_simulated_data()

            # Check for existing solar installations
            solar_panels = solar_potential.get('solarPanels', [])
            has_existing_solar = len(solar_panels) > 0

            # Get solar panel configurations if available
            solar_panel_configs = solar_potential.get('solarPanelConfigs', [])

            # Calculate potential metrics
            if solar_panel_configs:
                # Use the best configuration
                best_config = max(solar_panel_configs,
                                key=lambda x: x.get('yearlyEnergyDcKwh', 0))
                yearly_energy_kwh = best_config.get('yearlyEnergyDcKwh', 0)
                panel_capacity_kw = best_config.get('panelsCount', 0) * 0.4  # Assume 400W panels
            else:
                # Fallback calculation
                panel_capacity_kw = min(total_roof_area / 10, 15)  # Increased cap to 15kW
                yearly_energy_kwh = panel_capacity_kw * 1500

            # Carbon offset: ~0.5 kg CO2 per kWh
            carbon_offset_kg = yearly_energy_kwh * 0.5

            return {
                'success': True,
                'lat': building_lat,
                'lng': building_lng,
                'yearly_energy_kwh': yearly_energy_kwh,
                'roof_area_sqm': total_roof_area,
                'carbon_offset_kg': carbon_offset_kg,
                'panel_capacity_kw': panel_capacity_kw,
                'sunshine_hours': max_sunshine_hours,
                'has_existing_solar': has_existing_solar,
                'existing_panels_count': len(solar_panels),
                'data_quality': 'actual'
            }
        else:
            # Return simulated data if API fails
            return generate_simulated_data()

    except Exception as e:
        # Return simulated data on error
        return generate_simulated_data()

def generate_simulated_data():
    """Generate realistic simulated solar data for demo"""
    roof_area = random.uniform(80, 200)
    panel_capacity = min(roof_area / 10, 10)
    yearly_energy = panel_capacity * random.uniform(1300, 1700)
    
    return {
        'success': True,
        'yearly_energy_kwh': yearly_energy,
        'roof_area_sqm': roof_area,
        'carbon_offset_kg': yearly_energy * 0.5,
        'panel_capacity_kw': panel_capacity,
        'sunshine_hours': random.uniform(1800, 2200),
        'data_quality': 'simulated'
    }

def categorize_potential(yearly_energy_kwh):
    """Categorize solar potential based on yearly energy generation"""
    if yearly_energy_kwh >= 6000:
        return {
            'category': 'Excellent',
            'color': '#00ff00',
            'icon': '🟢',
            'priority': 1
        }
    elif yearly_energy_kwh >= 4000:
        return {
            'category': 'Good',
            'color': '#90EE90',
            'icon': '🟢',
            'priority': 2
        }
    elif yearly_energy_kwh >= 2500:
        return {
            'category': 'Moderate',
            'color': '#FFA500',
            'icon': '🟡',
            'priority': 3
        }
    else:
        return {
            'category': 'Low',
            'color': '#FF6B6B',
            'icon': '🔴',
            'priority': 4
        }

def find_buildings_in_area(center_lat, center_lng, radius_meters=200, max_buildings=15):
    """Find actual buildings with solar data using a more systematic approach"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return {
            'success': False,
            'error': 'Google API key not found'
        }

    buildings = []

    # Use a more systematic grid search with smaller increments
    # This will help find actual buildings more accurately
    lat_offset = radius_meters / 111000  # 1 degree lat ≈ 111km
    lng_offset = radius_meters / (111000 * max(abs(center_lat / 90), 0.1))

    # Create a denser grid (7x7) for better building detection
    grid_size = 7
    step_size = 2.0 / (grid_size - 1)  # Normalize to -1 to 1 range

    search_points = []
    for i in range(grid_size):
        for j in range(grid_size):
            # Create normalized coordinates from -1 to 1
            norm_i = -1 + i * step_size
            norm_j = -1 + j * step_size

            search_lat = center_lat + (norm_i * lat_offset)
            search_lng = center_lng + (norm_j * lng_offset)

            # Check if point is within radius
            distance = ((search_lat - center_lat) * 111000) ** 2 + \
                      ((search_lng - center_lng) * 111000 * max(abs(center_lat / 90), 0.1)) ** 2
            distance = distance ** 0.5

            if distance <= radius_meters:
                search_points.append((search_lat, search_lng))

    # Search for buildings at each point
    for search_lat, search_lng in search_points:
        if len(buildings) >= max_buildings:
            break

        building_data = get_solar_potential(search_lat, search_lng)

        if building_data['success']:
            # Use the actual building coordinates returned by the API
            building_lat = building_data.get('lat', search_lat)
            building_lng = building_data.get('lng', search_lng)

            # Check if we already have a building very close to this location
            is_duplicate = False
            for existing in buildings:
                lat_diff = abs(existing['lat'] - building_lat)
                lng_diff = abs(existing['lng'] - building_lng)
                if lat_diff < 0.0001 and lng_diff < 0.0001:  # ~10m threshold
                    is_duplicate = True
                    break

            if not is_duplicate:
                buildings.append({
                    'lat': building_lat,
                    'lng': building_lng,
                    'yearly_energy_kwh': building_data['yearly_energy_kwh'],
                    'roof_area_sqm': building_data['roof_area_sqm'],
                    'carbon_offset_kg': building_data['carbon_offset_kg'],
                    'panel_capacity_kw': building_data['panel_capacity_kw'],
                    'sunshine_hours': building_data.get('sunshine_hours', 0),
                    'data_source': building_data.get('data_quality', 'simulated'),
                    'has_existing_solar': building_data.get('has_existing_solar', False),
                    'address': f"Building at {building_lat:.5f}, {building_lng:.5f}"
                })

    return {
        'success': True,
        'buildings': buildings,
        'total_found': len(buildings)
    }

def get_building_address(lat, lng):
    """Get a proper address for a building using reverse geocoding"""
    api_key = os.getenv('GOOGLE_API_KEY')

    if not api_key:
        return f"Building at {lat:.5f}, {lng:.5f}"

    try:
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'latlng': f"{lat},{lng}",
            'key': api_key,
            'result_type': 'street_address|premise'
        }

        response = requests.get(url, params=params)
        data = response.json()

        if data['status'] == 'OK' and data['results']:
            return data['results'][0]['formatted_address']
        else:
            return f"Building at {lat:.5f}, {lng:.5f}"

    except Exception:
        return f"Building at {lat:.5f}, {lng:.5f}"